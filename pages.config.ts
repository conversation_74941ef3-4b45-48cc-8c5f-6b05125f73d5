/*
 * @Author: weish<PERSON>
 * @Date: 2025-06-23 22:23:05
 * @LastEditTime: 2025-08-15 21:37:50
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /tianxuan-mini/pages.config.ts
 * 记得注释
 */
import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'

export default defineUniPages({
  pages: [],
  globalStyle: {
    // 导航栏配置
    navigationBarBackgroundColor: '#ffffff',
    navigationBarTextStyle: 'black',
    navigationBarTitleText: 'Wot-Demo',
    // 页面背景配置
    backgroundColor: '#f5f5f5',
    backgroundTextStyle: 'light',
    backgroundColorTop: '#ffffff',
    backgroundColorBottom: '#f5f5f5',
    // 下拉刷新配置
    enablePullDownRefresh: false,
    onReachBottomDistance: 50,
    // 动画配置
    animationType: 'pop-in',
    animationDuration: 300,
  },
  tabBar: {
    custom: true,
    // #ifdef MP-ALIPAY
    customize: true,
    // 暂时不生效。4.71.2025061206-alpha已修复：https://uniapp.dcloud.net.cn/release-note-alpha.html#_4-71-2025061206-alpha，我们等正式版发布后更新。
    overlay: true,
    // #endif
    height: '0',
    color: '#999999',
    selectedColor: '#007aff',
    backgroundColor: '#ffffff',
    borderStyle: 'black',
    list: [{
      pagePath: 'pages/index/index',
    }, {
      pagePath: 'pages/about/index',
    }],
  },
})
