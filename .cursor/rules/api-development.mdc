---
description:
globs:
alwaysApply: false
---
# API开发规则

## Alova请求库使用规范

### 基础配置
1. 核心实例配置在 [src/api/core/instance.ts](mdc:src/api/core/instance.ts)
2. 全局API对象通过 [src/api/index.ts](mdc:src/api/index.ts) 导出
3. API定义使用 [src/api/apiDefinitions.ts](mdc:src/api/apiDefinitions.ts) 管理

### API使用方式
```typescript
// 导入全局API对象
import Apis from '@/api'

// 在组合式函数中使用
const { data, loading, error, send } = useRequest(Apis.user.getUserInfo)

// 直接调用
const userInfo = await Apis.user.getUserInfo()
```

### Mock数据管理
1. Mock数据存放在 `src/api/mock/modules/` 目录
2. 按业务模块分类：
   - [common.ts](mdc:src/api/mock/modules/common.ts) - 通用接口

### API生成命令
```bash
# 生成API接口
pnpm alova-gen

# 强制重新生成
pnpm alova-gen -f
```

### 配置特定API选项
在 [src/api/index.ts](mdc:src/api/index.ts) 中的 `$$userConfigMap` 对象中配置：
```typescript
export const $$userConfigMap = withConfigType({
  'api.methodName': {
    // 响应数据转换
    transform: (data: any) => data?.result || data,
    // 缓存配置
    cacheFor: 5 * 60 * 1000, // 5分钟
  },
})
```

### 错误处理
1. 全局错误处理在 [src/api/core/handlers.ts](mdc:src/api/core/handlers.ts) 中配置
2. 使用中间件进行请求/响应拦截
3. 结合 GlobalToast 组件显示错误信息

### 最佳实践
1. 优先使用 `useRequest` hook 进行状态管理
2. 合理配置缓存策略避免重复请求
3. 使用 TypeScript 类型定义确保类型安全
4. Mock数据与真实API保持一致的数据结构
