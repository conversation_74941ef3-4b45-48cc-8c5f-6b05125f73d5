{"name": "tianxuan-mini", "version": "0.0.0", "private": true, "packageManager": "pnpm@9.9.0", "license": "MIT", "scripts": {"dev": "uni", "dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build": "uni build", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "type-check": "vue-tsc --noEmit", "lint": "eslint .", "lint:fix": "eslint . --fix", "postinstall": "npx simple-git-hooks", "prepare": "husky install", "commit": "git-cz", "release-major": "standard-version --release-as major", "release-minor": "standard-version --release-as minor", "release-patch": "standard-version --release-as patch", "alova-gen": "alova gen -f"}, "dependencies": {"@alova/adapter-uniapp": "^2.0.13", "@alova/mock": "^2.0.16", "@alova/shared": "^1.3.0", "@dcloudio/uni-app": "3.0.0-4060620250520001", "@dcloudio/uni-app-harmony": "3.0.0-4060620250520001", "@dcloudio/uni-app-plus": "3.0.0-4060620250520001", "@dcloudio/uni-components": "3.0.0-4060620250520001", "@dcloudio/uni-h5": "3.0.0-4060620250520001", "@dcloudio/uni-mp-alipay": "3.0.0-4060620250520001", "@dcloudio/uni-mp-baidu": "3.0.0-4060620250520001", "@dcloudio/uni-mp-harmony": "3.0.0-4060620250520001", "@dcloudio/uni-mp-jd": "3.0.0-4060620250520001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4060620250520001", "@dcloudio/uni-mp-lark": "3.0.0-4060620250520001", "@dcloudio/uni-mp-qq": "3.0.0-4060620250520001", "@dcloudio/uni-mp-toutiao": "3.0.0-4060620250520001", "@dcloudio/uni-mp-weixin": "3.0.0-4060620250520001", "@dcloudio/uni-mp-xhs": "3.0.0-4060620250520001", "@dcloudio/uni-quickapp-webview": "3.0.0-4060620250520001", "@vueuse/core": "^11.0.3", "alova": "^3.3.2", "pinia": "^2.3.1", "vue": "~3.4.38", "vue-i18n": "^9.14.0", "wot-design-uni": "^1.11.1"}, "devDependencies": {"@alova/wormhole": "^1.0.6", "@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@dcloudio/types": "^3.4.12", "@dcloudio/uni-automator": "3.0.0-4060620250520001", "@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-stacktracey": "3.0.0-4060620250520001", "@dcloudio/uni-vue-devtools": "3.0.0-4020420240722002", "@dcloudio/vite-plugin-uni": "3.0.0-4060620250520001", "@iconify-json/carbon": "^1.1.37", "@mini-types/alipay": "^3.0.14", "@types/node": "^20.16.2", "@uni-helper/eslint-config": "^0.1.0", "@uni-helper/uni-env": "^0.1.4", "@uni-helper/uni-types": "^1.0.0-alpha.3", "@uni-helper/unocss-preset-uni": "^0.2.9", "@uni-helper/vite-plugin-uni-components": "^0.1.0", "@uni-helper/vite-plugin-uni-layouts": "^0.1.10", "@uni-helper/vite-plugin-uni-manifest": "^0.2.6", "@uni-helper/vite-plugin-uni-pages": "^0.2.27", "@uni-helper/volar-service-uni-pages": "^0.2.27", "@unocss/eslint-config": "^0.62.3", "@vue/runtime-core": "^3.4.38", "@vue/tsconfig": "^0.5.1", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.9.1", "git-cz": "^4.9.0", "husky": "^8.0.0", "lint-staged": "^15.2.9", "miniprogram-api-typings": "^3.12.3", "sass": "1.78.0", "simple-git-hooks": "^2.11.1", "standard-version": "^9.5.0", "typescript": "~5.5.4", "uni-mini-router": "^0.1.6", "unocss": "^0.62.3", "unplugin-auto-import": "^0.18.2", "vite": "^5.2.8", "vue-tsc": "^2.0.28"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}}