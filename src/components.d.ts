/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    DemoBlock: typeof import('./components/DemoBlock.vue')['default']
    GlobalLoading: typeof import('./components/GlobalLoading.vue')['default']
    GlobalMessage: typeof import('./components/GlobalMessage.vue')['default']
    GlobalToast: typeof import('./components/GlobalToast.vue')['default']
    PrivacyPopup: typeof import('./components/PrivacyPopup.vue')['default']
    WdButton: typeof import('wot-design-uni/components/wd-button/wd-button.vue')['default']
    WdCell: typeof import('wot-design-uni/components/wd-cell/wd-cell.vue')['default']
    WdCellGroup: typeof import('wot-design-uni/components/wd-cell-group/wd-cell-group.vue')['default']
    WdConfigProvider: typeof import('wot-design-uni/components/wd-config-provider/wd-config-provider.vue')['default']
    WdMessageBox: typeof import('wot-design-uni/components/wd-message-box/wd-message-box.vue')['default']
    WdNavbar: typeof import('wot-design-uni/components/wd-navbar/wd-navbar.vue')['default']
    WdNotify: typeof import('wot-design-uni/components/wd-notify/wd-notify.vue')['default']
    WdPopup: typeof import('wot-design-uni/components/wd-popup/wd-popup.vue')['default']
    WdTabbar: typeof import('wot-design-uni/components/wd-tabbar/wd-tabbar.vue')['default']
    WdTabbarItem: typeof import('wot-design-uni/components/wd-tabbar-item/wd-tabbar-item.vue')['default']
    WdToast: typeof import('wot-design-uni/components/wd-toast/wd-toast.vue')['default']
  }
}
