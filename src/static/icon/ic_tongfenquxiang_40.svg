<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_1541_3477)">
<circle cx="20" cy="20" r="20" fill="url(#paint0_radial_1541_3477)"/>
</g>
<path d="M18 9C18 8.44772 18.4477 8 19 8H21C21.5523 8 22 8.44772 22 9V31H18V9Z" fill="url(#paint1_linear_1541_3477)"/>
<path d="M10.4 20.8C10.7777 20.2964 11.3705 20 12 20H26C27.1046 20 28 20.8954 28 22V26C28 27.1046 27.1046 28 26 28H12C11.3705 28 10.7777 27.7036 10.4 27.2L8.9 25.2C8.36667 24.4889 8.36667 23.5111 8.9 22.8L10.4 20.8Z" fill="url(#paint2_linear_1541_3477)"/>
<path d="M28.3 11.4C28.1111 11.1482 27.8148 11 27.5 11H13C11.8954 11 11 11.8954 11 13V17C11 18.1046 11.8954 19 13 19H27.5C27.8148 19 28.1111 18.8518 28.3 18.6L30.55 15.6C30.8167 15.2444 30.8167 14.7556 30.55 14.4L28.3 11.4Z" fill="url(#paint3_linear_1541_3477)"/>
<rect x="10" y="30" width="20" height="2" rx="1" fill="url(#paint4_linear_1541_3477)"/>
<rect opacity="0.8" x="16" y="14" width="8" height="2" rx="1" fill="white"/>
<rect opacity="0.8" x="16" y="23" width="8" height="2" rx="1" fill="white"/>
<defs>
<filter id="filter0_ii_1541_3477" x="-1" y="-1" width="42" height="42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.168627 0 0 0 0 0.682353 0 0 0 0 0.956863 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1541_3477"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.794359 0 0 0 0 0.677388 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1541_3477" result="effect2_innerShadow_1541_3477"/>
</filter>
<radialGradient id="paint0_radial_1541_3477" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(20 20) rotate(86.0995) scale(24.5012)">
<stop stop-color="#070614"/>
<stop offset="0.519231" stop-color="#211D6E"/>
<stop offset="1" stop-color="#376EF4"/>
</radialGradient>
<linearGradient id="paint1_linear_1541_3477" x1="20" y1="31.5" x2="20" y2="8" gradientUnits="userSpaceOnUse">
<stop stop-color="#2E74FF"/>
<stop offset="1" stop-color="#B2CCFF"/>
</linearGradient>
<linearGradient id="paint2_linear_1541_3477" x1="9.5" y1="20" x2="27.5" y2="29" gradientUnits="userSpaceOnUse">
<stop stop-color="#F79B07"/>
<stop offset="1" stop-color="#F77307"/>
</linearGradient>
<linearGradient id="paint3_linear_1541_3477" x1="29.6325" y1="12.7031" x2="8.53574" y2="14.2677" gradientUnits="userSpaceOnUse">
<stop stop-color="#5E61FF"/>
<stop offset="1" stop-color="#003CB3"/>
</linearGradient>
<linearGradient id="paint4_linear_1541_3477" x1="6.875" y1="32" x2="29.9154" y2="29.6648" gradientUnits="userSpaceOnUse">
<stop stop-color="#2E74FF"/>
<stop offset="1" stop-color="#B2CCFF"/>
</linearGradient>
</defs>
</svg>
