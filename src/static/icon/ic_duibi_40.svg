<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_3002_2668)">
<circle cx="20" cy="20" r="20" fill="url(#paint0_radial_3002_2668)"/>
</g>
<rect x="19" y="10" width="2" height="20" rx="1" fill="url(#paint1_linear_3002_2668)"/>
<rect x="9" y="12" width="8" height="16" rx="2" fill="url(#paint2_linear_3002_2668)"/>
<rect opacity="0.8" x="12" y="22" width="4" height="2" rx="1" transform="rotate(-90 12 22)" fill="white"/>
<rect x="23" y="12" width="8" height="16" rx="2" fill="url(#paint3_linear_3002_2668)"/>
<rect opacity="0.8" x="26" y="22" width="4" height="2" rx="1" transform="rotate(-90 26 22)" fill="white"/>
<defs>
<filter id="filter0_ii_3002_2668" x="-1" y="-1" width="42" height="42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.168627 0 0 0 0 0.682353 0 0 0 0 0.956863 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3002_2668"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.794359 0 0 0 0 0.677388 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_3002_2668" result="effect2_innerShadow_3002_2668"/>
</filter>
<radialGradient id="paint0_radial_3002_2668" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(20 20) rotate(86.0995) scale(24.5012)">
<stop stop-color="#070614"/>
<stop offset="0.519231" stop-color="#211D6E"/>
<stop offset="1" stop-color="#376EF4"/>
</radialGradient>
<linearGradient id="paint1_linear_3002_2668" x1="20" y1="30.4348" x2="20" y2="10" gradientUnits="userSpaceOnUse">
<stop stop-color="#2E74FF"/>
<stop offset="1" stop-color="#B2CCFF"/>
</linearGradient>
<linearGradient id="paint2_linear_3002_2668" x1="9.6" y1="12" x2="18.5109" y2="12.8911" gradientUnits="userSpaceOnUse">
<stop stop-color="#F79B07"/>
<stop offset="1" stop-color="#F77307"/>
</linearGradient>
<linearGradient id="paint3_linear_3002_2668" x1="23.547" y1="15.4063" x2="32.0302" y2="15.5321" gradientUnits="userSpaceOnUse">
<stop stop-color="#5E61FF"/>
<stop offset="1" stop-color="#003CB3"/>
</linearGradient>
</defs>
</svg>
