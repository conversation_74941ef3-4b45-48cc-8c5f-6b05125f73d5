<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_3002_2693)">
<circle cx="20" cy="20" r="20" fill="url(#paint0_radial_3002_2693)"/>
</g>
<path d="M9 12.1174C9 11.423 9.36229 10.7752 10.0137 10.5346C10.7203 10.2736 11.7574 10 13 10C15.3755 10 20 11 20 11V30C20 30 15.3755 28 13 28C12.7989 28 12.6032 28.0072 12.4134 28.0203C10.8524 28.1282 9 26.9851 9 25.4203V12.1174Z" fill="url(#paint1_linear_3002_2693)"/>
<path d="M32 11.5587C32 11.2115 31.8198 10.8898 31.5035 10.7465C30.8497 10.4502 29.5939 10 28 10C25.6245 10 21 11 21 11V30C21 30 25.6245 28 28 28C28.9084 28 29.7069 28.1462 30.3468 28.3268C31.0998 28.5394 32 27.9925 32 27.2102V11.5587Z" fill="url(#paint2_linear_3002_2693)"/>
<path opacity="0.8" d="M18 17H16C14.3431 17 13 18.3431 13 20V20C13 21.6569 14.3431 23 16 23H18" stroke="white" stroke-width="2" stroke-linecap="round"/>
<path opacity="0.8" d="M23 17H25C26.6569 17 28 18.3431 28 20V20C28 21.6569 26.6569 23 25 23H23" stroke="white" stroke-width="2" stroke-linecap="round"/>
<rect x="17" y="19" width="7" height="2" rx="1" fill="url(#paint3_linear_3002_2693)"/>
<defs>
<filter id="filter0_ii_3002_2693" x="-1" y="-1" width="42" height="42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.168627 0 0 0 0 0.682353 0 0 0 0 0.956863 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3002_2693"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.794359 0 0 0 0 0.677388 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_3002_2693" result="effect2_innerShadow_3002_2693"/>
</filter>
<radialGradient id="paint0_radial_3002_2693" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(20 20) rotate(86.0995) scale(24.5012)">
<stop stop-color="#070614"/>
<stop offset="0.519231" stop-color="#211D6E"/>
<stop offset="1" stop-color="#376EF4"/>
</radialGradient>
<linearGradient id="paint1_linear_3002_2693" x1="9.825" y1="10" x2="22.0521" y2="11.345" gradientUnits="userSpaceOnUse">
<stop stop-color="#F79B07"/>
<stop offset="1" stop-color="#F77307"/>
</linearGradient>
<linearGradient id="paint2_linear_3002_2693" x1="32.1795" y1="14.2578" x2="19.4559" y2="14.4843" gradientUnits="userSpaceOnUse">
<stop stop-color="#5E61FF"/>
<stop offset="1" stop-color="#003CB3"/>
</linearGradient>
<linearGradient id="paint3_linear_3002_2693" x1="15.9062" y1="21" x2="24.043" y2="20.7114" gradientUnits="userSpaceOnUse">
<stop stop-color="#2E74FF"/>
<stop offset="1" stop-color="#B2CCFF"/>
</linearGradient>
</defs>
</svg>
