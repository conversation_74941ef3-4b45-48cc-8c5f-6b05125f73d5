<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_1541_3481)">
<circle cx="20" cy="20" r="20" fill="url(#paint0_radial_1541_3481)"/>
</g>
<path d="M14.5528 10.7236C14.214 10.893 14 11.2393 14 11.618L14 30L26 30L26 11.618C26 11.2393 25.786 10.893 25.4472 10.7236L20.4472 8.22361C20.1657 8.08284 19.8343 8.08284 19.5528 8.22361L14.5528 10.7236Z" fill="url(#paint1_linear_1541_3481)"/>
<path d="M10 18C10 16.8954 10.8954 16 12 16H14V30H12C10.8954 30 10 29.1046 10 28V18Z" fill="url(#paint2_linear_1541_3481)"/>
<path d="M26 16H28C29.1046 16 30 16.8954 30 18V28C30 29.1046 29.1046 30 28 30H26V16Z" fill="url(#paint3_linear_1541_3481)"/>
<circle opacity="0.8" cx="20" cy="13" r="2" fill="white"/>
<path d="M17 25C17 24.4477 17.4477 24 18 24H22C22.5523 24 23 24.4477 23 25V30H17V25Z" fill="url(#paint4_linear_1541_3481)"/>
<rect x="8" y="30" width="24" height="2" rx="1" fill="url(#paint5_linear_1541_3481)"/>
<defs>
<filter id="filter0_ii_1541_3481" x="-1" y="-1" width="42" height="42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.168627 0 0 0 0 0.682353 0 0 0 0 0.956863 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1541_3481"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.794359 0 0 0 0 0.677388 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1541_3481" result="effect2_innerShadow_1541_3481"/>
</filter>
<radialGradient id="paint0_radial_1541_3481" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(20 20) rotate(86.0995) scale(24.5012)">
<stop stop-color="#070614"/>
<stop offset="0.519231" stop-color="#211D6E"/>
<stop offset="1" stop-color="#376EF4"/>
</radialGradient>
<linearGradient id="paint1_linear_1541_3481" x1="16.5547" y1="9.50427" x2="17.82" y2="32.7695" gradientUnits="userSpaceOnUse">
<stop stop-color="#5E61FF"/>
<stop offset="1" stop-color="#003CB3"/>
</linearGradient>
<linearGradient id="paint2_linear_1541_3481" x1="10.3" y1="16" x2="14.7854" y2="16.2563" gradientUnits="userSpaceOnUse">
<stop stop-color="#F79B07"/>
<stop offset="1" stop-color="#F77307"/>
</linearGradient>
<linearGradient id="paint3_linear_1541_3481" x1="30" y1="32.5" x2="24.9776" y2="32.3488" gradientUnits="userSpaceOnUse">
<stop stop-color="#F79B07"/>
<stop offset="1" stop-color="#F77307"/>
</linearGradient>
<linearGradient id="paint4_linear_1541_3481" x1="20" y1="30.1304" x2="20" y2="24" gradientUnits="userSpaceOnUse">
<stop stop-color="#2E74FF"/>
<stop offset="1" stop-color="#B2CCFF"/>
</linearGradient>
<linearGradient id="paint5_linear_1541_3481" x1="4.25" y1="32" x2="31.7753" y2="28.6523" gradientUnits="userSpaceOnUse">
<stop stop-color="#2E74FF"/>
<stop offset="1" stop-color="#B2CCFF"/>
</linearGradient>
</defs>
</svg>
