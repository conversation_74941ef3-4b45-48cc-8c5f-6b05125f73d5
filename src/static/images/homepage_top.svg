<svg width="375" height="216" viewBox="0 0 375 216" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 0H375V177C375 177 260.723 216 187.5 216C114.277 216 0 177 0 177V0Z" fill="#1E1671"/>
<g opacity="0.3" filter="url(#filter0_f_1690_2098)">
<circle cx="77.5" cy="66.5" r="19.5" fill="white"/>
</g>
<g opacity="0.3" filter="url(#filter1_f_1690_2098)">
<circle cx="328" cy="79" r="30" fill="white"/>
</g>
<g filter="url(#filter2_i_1690_2098)">
<path d="M58.56 77.424V102H47.952V77.424H37.104V68.976H69.36V77.424H58.56ZM75.6726 102V68.976H86.2806V102H75.6726ZM116.977 102L115.585 97.344H104.017L102.625 102H91.7769L103.825 68.976H116.161L128.209 102H116.977ZM106.225 89.952H113.377L109.921 78.192H109.729L106.225 89.952ZM148.781 68.4C154.477 68.4 158.877 69.856 161.981 72.768C165.085 75.68 166.637 79.92 166.637 85.488C166.637 91.056 165.085 95.296 161.981 98.208C158.877 101.12 154.477 102.576 148.781 102.576C143.085 102.576 138.685 101.136 135.581 98.256C132.509 95.344 130.973 91.088 130.973 85.488C130.973 79.888 132.509 75.648 135.581 72.768C138.685 69.856 143.085 68.4 148.781 68.4ZM148.781 76.32C146.509 76.32 144.781 77.008 143.597 78.384C142.413 79.76 141.821 81.616 141.821 83.952V87.024C141.821 89.36 142.413 91.216 143.597 92.592C144.781 93.968 146.509 94.656 148.781 94.656C151.053 94.656 152.781 93.968 153.965 92.592C155.181 91.216 155.789 89.36 155.789 87.024V83.952C155.789 81.616 155.181 79.76 153.965 78.384C152.781 77.008 151.053 76.32 148.781 76.32ZM198.261 90.528C198.261 94.656 197.077 97.696 194.709 99.648C192.373 101.6 188.997 102.576 184.581 102.576C180.165 102.576 176.773 101.6 174.405 99.648C172.069 97.696 170.901 94.656 170.901 90.528V90.096H180.837V90.48C180.837 91.984 181.109 93.056 181.653 93.696C182.229 94.336 183.093 94.656 184.245 94.656C185.397 94.656 186.245 94.336 186.789 93.696C187.365 93.056 187.653 91.984 187.653 90.48V68.976H198.261V90.528ZM206.845 102V68.976H217.453V102H206.845ZM248.005 68.976C249.669 68.976 251.189 69.328 252.565 70.032C253.973 70.704 255.077 71.664 255.877 72.912C256.709 74.16 257.125 75.552 257.125 77.088C257.125 81.152 255.285 83.712 251.605 84.768V84.96C255.797 85.92 257.893 88.672 257.893 93.216C257.893 94.944 257.461 96.48 256.597 97.824C255.765 99.136 254.613 100.16 253.141 100.896C251.669 101.632 250.053 102 248.293 102H226.021V68.976H248.005ZM236.629 81.84H243.925C244.629 81.84 245.205 81.6 245.653 81.12C246.133 80.608 246.373 79.984 246.373 79.248V78.768C246.373 78.064 246.133 77.472 245.653 76.992C245.173 76.48 244.597 76.224 243.925 76.224H236.629V81.84ZM236.629 94.32H244.693C245.397 94.32 245.973 94.08 246.421 93.6C246.901 93.088 247.141 92.464 247.141 91.728V91.248C247.141 90.512 246.901 89.904 246.421 89.424C245.973 88.912 245.397 88.656 244.693 88.656H236.629V94.32ZM285.711 102L284.319 97.344H272.751L271.359 102H260.511L272.559 68.976H284.895L296.943 102H285.711ZM274.959 89.952H282.111L278.655 78.192H278.463L274.959 89.952ZM317.515 68.4C323.211 68.4 327.611 69.856 330.715 72.768C333.819 75.68 335.371 79.92 335.371 85.488C335.371 91.056 333.819 95.296 330.715 98.208C327.611 101.12 323.211 102.576 317.515 102.576C311.819 102.576 307.419 101.136 304.315 98.256C301.243 95.344 299.707 91.088 299.707 85.488C299.707 79.888 301.243 75.648 304.315 72.768C307.419 69.856 311.819 68.4 317.515 68.4ZM317.515 76.32C315.243 76.32 313.515 77.008 312.331 78.384C311.147 79.76 310.555 81.616 310.555 83.952V87.024C310.555 89.36 311.147 91.216 312.331 92.592C313.515 93.968 315.243 94.656 317.515 94.656C319.787 94.656 321.515 93.968 322.699 92.592C323.915 91.216 324.523 89.36 324.523 87.024V83.952C324.523 81.616 323.915 79.76 322.699 78.384C321.515 77.008 319.787 76.32 317.515 76.32Z" fill="white" fill-opacity="0.06"/>
</g>
<defs>
<filter id="filter0_f_1690_2098" x="28" y="17" width="99" height="99" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="15" result="effect1_foregroundBlur_1690_2098"/>
</filter>
<filter id="filter1_f_1690_2098" x="268" y="19" width="120" height="120" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="15" result="effect1_foregroundBlur_1690_2098"/>
</filter>
<filter id="filter2_i_1690_2098" x="37.104" y="68.4" width="299.267" height="35.176" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1690_2098"/>
</filter>
</defs>
</svg>
