/// <reference types='./globals.d.ts' />
/* tslint:disable */
/* eslint-disable */
/**
 * 天枢_接口文档 - version 版本号:3.8.8
 *
 * 用于小程序接口对接; 请求头要新增appId字段, 老调剂宝传tjb; 新的传ntjb
 *
 * OpenAPI version: 3.0.3
 *
 * Contact:
 *
 * NOTE: This file is auto generated by the alova's vscode plugin.
 *
 * https://alova.js.org/devtools/vscode
 *
 * **Do not edit the file manually.**
 */
export default {
  'xcx.clearCacheKeyUsingDELETE': ['DELETE', '/monitor/cache/clearCacheKey/{cacheKey}'],
  'xcx.getCacheValueUsingGET': ['GET', '/monitor/cache/getValue/{cacheName}/{cacheKey}'],
  'xcxFeedback.createUsingPOST': ['POST', '/xcx/feedback/create'],
  'xcxFeedback.listUsingPOST': ['POST', '/xcx/feedback/list'],
  'xcxMember.updateAvatarUsingPOST': ['POST', '/xcx/member/avatar/update'],
  'xcxMember.payH5UsingGET': ['GET', '/xcx/member/buy/pay/h5'],
  'xcxMember.payPreCreateUsingPOST': ['POST', '/xcx/member/buy/payPreCreate'],
  'xcxMember.payQueryUsingPOST': ['POST', '/xcx/member/buy/payQuery'],
  'xcxMember.getPriceUsingPOST': ['POST', '/xcx/member/buy/price'],
  'xcxMember.canPopupUsingGET': ['GET', '/xcx/member/canPopup'],
  'xcxMember.code2SessionUsingPOST': ['POST', '/xcx/member/code2Session'],
  'xcxMember.addCouponUsingPOST': ['POST', '/xcx/member/coupon/add'],
  'xcxMember.listCouponUsingPOST': ['POST', '/xcx/member/coupon/list'],
  'xcxMember.decryptWxUsingPOST': ['POST', '/xcx/member/decryptWx'],
  'xcxMember.freeUsageCountUsingGET': ['GET', '/xcx/member/freeuage/count'],
  'xcxMember.infoUsingPOST': ['POST', '/xcx/member/info'],
  'xcxMember.loginUsingPOST': ['POST', '/xcx/member/login'],
  'xcxMember.updateNicknameUsingPOST': ['POST', '/xcx/member/nickname/update'],
  'xcxMember.updateProfileUsingPOST': ['POST', '/xcx/member/profile/update'],
  'xcxMember.recordPopTimesUsingPOST': ['POST', '/xcx/member/recordPopTimes'],
  'xcxMember.registerUsingPOST': ['POST', '/xcx/member/register'],
  'xcxMember.remainUpdateTimesUsingGET': ['GET', '/xcx/member/remainUpdateTimes'],
  'xcxMember.rightsUsingPOST': ['POST', '/xcx/member/rights'],
  'xcxMember.startExpireJobUsingPOST': ['POST', '/xcx/member/startExpireJob'],
  'xcxNews.queryUsingPOST': ['POST', '/xcx/news/query'],
  'xcxUniversity.adjustAnalysisResultUsingPOST': ['POST', '/xcx/university/adjust/analysis/result'],
  'xcxUniversity.sameScoreDest4AdjustAnalysisUsingPOST': ['POST', '/xcx/university/adjust/analysis/sameScore/dest'],
  'xcxUniversity.sameUniDest4AdjustAnalysisUsingPOST': ['POST', '/xcx/university/adjust/analysis/sameUni/dest'],
  'xcxUniversity.queryAdmissionBaseUsingPOST': ['POST', '/xcx/university/admission/base'],
  'xcxUniversity.queryMajorDetailUsingPOST': ['POST', '/xcx/university/admission/last3Years'],
  'xcxUniversity.queryLast3YearsScoreRangeUsingPOST': ['POST', '/xcx/university/admission/last3YearsScoreRange'],
  'xcxUniversity.queryProposedNameUsingPOST': ['POST', '/xcx/university/admission/proposed/name'],
  'xcxUniversity.queryAdmissionMajorCatalogUsingPOST': ['POST', '/xcx/university/admission_major_catalog/query'],
  'xcxUniversity.listCodeNameUsingPOST': ['POST', '/xcx/university/codeName/list'],
  'xcxUniversity.compareUsingPOST': ['POST', '/xcx/university/compare'],
  'xcxUniversity.queryDepartmentForRegisterUsingPOST': ['POST', '/xcx/university/department/queryForRegister'],
  'xcxUniversity.queryDirectionForRegisterUsingPOST': ['POST', '/xcx/university/direction/queryForRegister'],
  'xcxUniversity.collectUsingPOST': ['POST', '/xcx/university/fav/collect'],
  'xcxUniversity.favListUsingPOST': ['POST', '/xcx/university/fav/list'],
  'xcxUniversity.subscribeUsingPOST': ['POST', '/xcx/university/fav/subscribe'],
  'xcxUniversity.unCollectUsingPOST': ['POST', '/xcx/university/fav/unCollect'],
  'xcxUniversity.unSubscribeUsingPOST': ['POST', '/xcx/university/fav/unSubscribe'],
  'xcxUniversity.queryMyRankUsingPOST': ['POST', '/xcx/university/fightAnalysis/myRank'],
  'xcxUniversity.queryOneScoreOneRangeUsingPOST': ['POST', '/xcx/university/fightAnalysis/oneScoreOneRange'],
  'xcxUniversity.querySameScoreDestUsingPOST': ['POST', '/xcx/university/fightAnalysis/sameScore/dest'],
  'xcxUniversity.sameScoreDestStatisticsUsingPOST': ['POST', '/xcx/university/fightAnalysis/sameScore/dest/statistics'],
  'xcxUniversity.querySameUniDestUsingPOST': ['POST', '/xcx/university/fightAnalysis/sameUni/dest'],
  'xcxUniversity.querySameUniDestStatisticsUsingPOST': [
    'POST',
    '/xcx/university/fightAnalysis/sameUni/dest/statistics'
  ],
  'xcxUniversity.firstChoiceYearAnalysisUsingPOST': ['POST', '/xcx/university/firstChoice/analysis/year'],
  'xcxUniversity.firstChoicePredictUsingPOST': ['POST', '/xcx/university/firstChoice/predict'],
  'xcxUniversity.levelsUsingGET': ['GET', '/xcx/university/levels'],
  'xcxUniversity.majorDetailUsingPOST': ['POST', '/xcx/university/major/detail'],
  'xcxUniversity.queryMajorForRegisterUsingPOST': ['POST', '/xcx/university/major/queryForRegister'],
  'xcxUniversity.listQueryConditionUsingPOST': ['POST', '/xcx/university/qd/list'],
  'xcxUniversity.queryByMajorUsingPOST': ['POST', '/xcx/university/queryByMajor'],
  'xcxUniversity.queryByUniUsingPOST': ['POST', '/xcx/university/queryByUni'],
  'xcxUniversity.queryForRegisterUsingPOST': ['POST', '/xcx/university/queryForRegister'],
  'xcxUniversity.queryUniMajorUsingPOST': ['POST', '/xcx/university/queryUniMajor'],
  'xcxUniversity.scoreChartUsingPOST': ['POST', '/xcx/university/scoreChart'],
  'xcxUniversity.serverLast3YearUsingGET': ['GET', '/xcx/university/serverLast3Year'],
  'xcxFile.uploadUsingPOST': ['POST', '/xcx/upload'],
  'xcxWxmp.openUsingGET': ['GET', '/xcx/weixin/code'],
  'xcxWxmp.code2SessionUsingGET': ['GET', '/xcx/weixin/code2session']
};
