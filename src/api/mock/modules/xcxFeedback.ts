/*
 * @Author: SOLO Coding
 * @Date: 2025-01-18 10:00:00
 * @LastEditTime: 2025-01-18 10:00:00
 * @LastEditors: SOLO Coding
 * @Description: xcxFeedback反馈相关接口的mock数据
 * @FilePath: /tianxuan-mini/src/api/mock/modules/xcxFeedback.ts
 */
import { defineMock } from '@alova/mock'
import { generateMockData } from '../utils/generators'
import type {
  Baseresponse_void,
  Baseresponse_list_feedbackvo,
  FeedBackRequest,
  FeedBackVo,
  FeedBackFilter,
} from '../../globals.d'

export default defineMock({
  // 添加反馈
  '[POST]/xcx/feedback/add': ({ data }: { data: FeedBackRequest }) => {
    console.log('[Mock] POST /xcx/feedback/add', data)
    return generateMockData.baseResponse(null, 2000, '反馈提交成功') as Baseresponse_void
  },

  // 获取反馈列表
  '[POST]/xcx/feedback/list': ({ data }: { data?: FeedBackFilter }) => {
    console.log('[Mock] POST /xcx/feedback/list', data)
    
    const feedbackList: FeedBackVo[] = generateMockData.array(() => ({
      id: generateMockData.id(),
      title: `${['功能建议', '问题反馈', '使用体验', '界面优化', '数据准确性'][generateMockData.number(0, 4)]}`,
      content: `这是一条${['很有价值的', '重要的', '紧急的', '一般的'][generateMockData.number(0, 3)]}反馈内容，希望能够得到及时处理和回复。`,
      type: ['Bug', '数据纠错', '建议', '吐槽', '其他'][generateMockData.number(0, 4)], // 反馈类型1功能建议 2问题反馈 3使用体验 4界面优化 5数据准确性
      status: generateMockData.number(0, 3), // 0待处理 1处理中 2已处理 3已关闭
      priority: generateMockData.number(1, 3), // 1低 2中 3高
      createTime: generateMockData.datetime(),
      updateTime: generateMockData.datetime(),
      userId: generateMockData.id(),
      userName: `用户${generateMockData.number(1000, 9999)}`,
      reply: generateMockData.boolean() ? `感谢您的反馈，我们会认真考虑您的建议。` : undefined,
      replyTime: generateMockData.boolean() ? generateMockData.datetime() : undefined,
    } as FeedBackVo), 5)
    
    return generateMockData.baseResponse(feedbackList) as Baseresponse_list_feedbackvo
  },

  // 更新反馈状态
  '[POST]/xcx/feedback/updateStatus': ({ data }: { data: { id: string; status: number } }) => {
    console.log('[Mock] POST /xcx/feedback/updateStatus', data)
    return generateMockData.baseResponse(null, 2000, '反馈状态更新成功') as Baseresponse_void
  },

  // 回复反馈
  '[POST]/xcx/feedback/reply': ({ data }: { data: { id: string; reply: string } }) => {
    console.log('[Mock] POST /xcx/feedback/reply', data)
    return generateMockData.baseResponse(null, 2000, '反馈回复成功') as Baseresponse_void
  },

  // 删除反馈
  '[DELETE]/xcx/feedback/delete': ({ query }: { query: { id: string } }) => {
    console.log('[Mock] DELETE /xcx/feedback/delete', query)
    return generateMockData.baseResponse(null, 2000, '反馈删除成功') as Baseresponse_void
  },
})