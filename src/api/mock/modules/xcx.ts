/*
 * @Author: SOLO Coding
 * @Date: 2025-01-18 10:00:00
 * @LastEditTime: 2025-01-18 10:00:00
 * @LastEditors: SOLO Coding
 * @Description: xcx相关接口的mock数据
 * @FilePath: /tianxuan-mini/src/api/mock/modules/xcx.ts
 */
import { defineMock } from '@alova/mock'
import { generateMockData } from '../utils/generators'
import type {
  Baseresponse_void,
} from '../../globals.d'

export default defineMock({
  // 清除缓存
  '[DELETE]/xcx/clearCacheKey': ({ query }: { query?: { key?: string } }) => {
    console.log('[Mock] DELETE /xcx/clearCacheKey', query)
    return generateMockData.baseResponse(null, 2000, '缓存清除成功') as Baseresponse_void
  },
})