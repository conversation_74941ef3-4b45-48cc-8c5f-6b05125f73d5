/*
 * @Author: S<PERSON>O Coding
 * @Date: 2025-01-18 10:00:00
 * @LastEditTime: 2025-08-17 17:13:14
 * @LastEditors: weisheng
 * @Description: xcxSyb志愿预测相关接口的mock数据
 * @FilePath: /tianxuan-mini/src/api/mock/modules/xcxSyb.ts
 */
import { defineMock } from '@alova/mock'
import { generateMockData } from '../utils/generators'
import type {
  Baseresponse_sybfirstchoicepredictvo,
  Baseresponse_void,
  FirstChoicePredictNationLineVo,
  SybFirstChoicePredictAdmissionIntervalRequest,
  SybFirstChoicePredictAdmissionRequest,
  SybFirstChoicePredictVo,
} from '../../globals.d'

export default defineMock({
  // 一志愿预测线
  '[POST]/xcx/syb/firstChoicePredictLine': ({ data }: { data: SybFirstChoicePredictAdmissionRequest }) => {
    console.log('[Mock] POST /xcx/syb/firstChoicePredictLine', data)

    const predictVo: SybFirstChoicePredictVo = {
      firstChoiceSecondExamChanceDesc: generateMockData.number(20, 90),
      firstChoiceToAdmChanceDesc: generateMockData.number(10, 80),
      lastYearFirstChoiceSecondExamMin: generateMockData.number(300, 400),
      lastYearFirstChoiceSecondExamNum: generateMockData.number(50, 200),
      lastYearFirstChoiceSecondExamRank: generateMockData.number(1, 100),
      lastYearFirstChoiceToAdmMin: generateMockData.number(320, 450),
      lastYearFirstChoiceToAdmNum: generateMockData.number(20, 100),
      lastYearFirstChoiceToAdmRank: generateMockData.number(1, 50),
      lastYearSamePositionScore: generateMockData.number(300, 400),
      nowYLastYLineTotalScoreDiff: generateMockData.number(-20, 20),
      nationalLines: generateMockData.array(() => ({
        year: generateMockData.number(2020, 2024).toString(),
        totalScore: generateMockData.number(280, 350),
        type: generateMockData.number(1, 3), // 1学硕 2专硕 3博士
      } as FirstChoicePredictNationLineVo), 3),
    }

    return generateMockData.baseResponse(predictVo) as Baseresponse_sybfirstchoicepredictvo
  },

  // 一志愿预测录取区间
  '[POST]/xcx/syb/firstChoicePredictAdmissionInterval': ({ data }: { data: SybFirstChoicePredictAdmissionIntervalRequest }) => {
    console.log('[Mock] POST /xcx/syb/firstChoicePredictAdmissionInterval', data)
    return generateMockData.baseResponse(null, 2000, '预测录取区间计算完成') as Baseresponse_void
  },
})
