/*
 * @Author: SOLO Coding
 * @Date: 2025-01-18 10:00:00
 * @LastEditTime: 2025-01-18 10:00:00
 * @LastEditors: SOLO Coding
 * @Description: xcxUniversity大学相关接口的mock数据
 * @FilePath: /tianxuan-mini/src/api/mock/modules/xcxUniversity.ts
 */
import { defineMock } from '@alova/mock'
import { generateMockData } from '../utils/generators'
import type {
  Baseresponse_void,
} from '../../globals.d'

export default defineMock({
  // 调整分析结果
  '[POST]/xcx/university/adjustAnalysisResult': ({ data }: { data?: any }) => {
    console.log('[Mock] POST /xcx/university/adjustAnalysisResult', data)
    return generateMockData.baseResponse(null, 2000, '分析结果调整成功') as Baseresponse_void
  },

  // 分析结果
  '[POST]/xcx/university/analysisResult': ({ data }: { data?: any }) => {
    console.log('[Mock] POST /xcx/university/analysisResult', data)
    const analysisResult = {
      totalSchools: generateMockData.number(50, 200),
      matchedSchools: generateMockData.number(10, 50),
      recommendedSchools: generateMockData.array(() => ({
        schoolCode: generateMockData.code('SCHOOL'),
        schoolName: `${['北京', '清华', '复旦', '上海交通', '浙江', '中南', '华中科技', '西安交通'][generateMockData.number(0, 7)]}大学`,
        probability: generateMockData.number(0.1, 0.9),
        lastYearScore: generateMockData.number(300, 450),
        predictScore: generateMockData.number(300, 450),
      }), 5),
      analysisTime: generateMockData.datetime(),
    }
    return generateMockData.baseResponse(analysisResult)
  },

  // 批量分析
  '[POST]/xcx/university/batchAnalysis': ({ data }: { data?: any }) => {
    console.log('[Mock] POST /xcx/university/batchAnalysis', data)
    return generateMockData.baseResponse(null, 2000, '批量分析任务已启动') as Baseresponse_void
  },

  // 获取分析历史
  '[GET]/xcx/university/analysisHistory': ({ query }: { query?: any }) => {
    console.log('[Mock] GET /xcx/university/analysisHistory', query)
    const historyList = generateMockData.array(() => ({
      id: generateMockData.id(),
      analysisTime: generateMockData.datetime(),
      schoolCount: generateMockData.number(10, 100),
      status: generateMockData.number(0, 2), // 0进行中 1完成 2失败
      result: generateMockData.boolean() ? '分析完成' : '分析中...',
    }), 3)
    return generateMockData.baseResponse(historyList)
  },
})