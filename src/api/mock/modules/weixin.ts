/*
 * @Author: weish<PERSON>
 * @Date: 2025-08-17 16:00:00
 * @LastEditTime: 2025-08-17 16:09:26
 * @LastEditors: weisheng
 * @Description: 微信相关接口的mock数据
 * @FilePath: /tianxuan-mini/src/api/mock/modules/weixin.ts
 */
import { defineMock } from '@alova/mock'
import { generateMockData } from '../utils/generators'
import type { Baseresponse1, Gejdec } from '../../globals.d'

export default defineMock({
  // 微信小程序码
  '[GET]/xcx/weixin/code': ({ query }) => {
    console.log('[Mock] GET /xcx/weixin/code', query)

    const qrcodeUrl = `https://example.com/wxcode/${generateMockData.code('WXCODE')}.jpg`

    return generateMockData.baseResponse(qrcodeUrl)
  },

  // 微信code2session
  '[GET]/xcx/weixin/code2session': ({ query }: { query?: { code?: string } }) => {
    console.log('[Mock] GET /xcx/weixin/code2session', query)

    if (!query?.code) {
      return {
        code: 400,
        data: null,
        msg: '授权码不能为空',
      }
    }

    if (query.code === 'invalid_code') {
      return {
        code: 401,
        data: null,
        msg: '授权码无效或已过期',
      }
    }

    const memberInfo: Gejdec = {
      id: generateMockData.id().toString(),
      openid: `openid_${generateMockData.code('WX')}`,
      nickName: generateMockData.name('用户'),
      mobile: `1${generateMockData.number(3000000000, 9999999999)}`,
      avatar: `https://example.com/avatar/${generateMockData.code('AVATAR')}.jpg`,
      name: generateMockData.name('考生'),
      examRegistrationNumber: generateMockData.code('EXAM'),
      firstChoiceSchoolCode: generateMockData.code('SCHOOL'),
      firstChoiceSchoolName: `${['北京', '清华', '复旦', '上海交通', '浙江'][generateMockData.number(0, 4)]}大学`,
      firstChoiceDegreeCode: generateMockData.code('DEGREE'),
      firstChoiceDegreeName: `${['计算机科学与技术', '软件工程', '电子信息', '机械工程', '工商管理'][generateMockData.number(0, 4)]}`,
      departmentCode: generateMockData.code('DEPT'),
      departmentName: `${['计算机学院', '软件学院', '信息学院', '机械学院', '管理学院'][generateMockData.number(0, 4)]}`,
      directionCode: generateMockData.code('DIR'),
      directionName: `${['人工智能', '大数据', '网络安全', '软件开发', '系统架构'][generateMockData.number(0, 4)]}`,
      firstScore: generateMockData.number(300, 500),
      politicsScore: generateMockData.number(60, 100),
      foreignLanguageScore: generateMockData.number(60, 100),
      biz1Score: generateMockData.number(60, 150),
      biz2Score: generateMockData.number(60, 150),
      studyType: generateMockData.boolean() ? '1' : '0',
    }

    return generateMockData.baseResponse(memberInfo) as Baseresponse1
  },
})
