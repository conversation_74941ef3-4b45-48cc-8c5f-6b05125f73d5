/*
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-08-17 16:00:00
 * @LastEditTime: 2025-08-17 16:51:11
 * @LastEditors: weisheng
 * @Description: 新闻相关接口的mock数据
 * @FilePath: /tianxuan-mini/src/api/mock/modules/news.ts
 */
import { defineMock } from '@alova/mock'
import { generateMockData } from '../utils/generators'
import type { Baseresponse_list_news, News, NewsFilter } from '../../globals.d'

export default defineMock({
  // 新闻查询
  '[POST]/xcx/news/query': ({ data }: { data: NewsFilter }) => {
    console.log('[Mock] POST /xcx/news/query', data)

    const newsTypes = ['招生简章', '复试通知', '调剂信息', '录取公告', '考试安排', '政策解读', '院校动态']
    const sources = ['官网', '研招网', '学院官网', '教育部', '省教育厅']
    const universities = ['北京大学', '清华大学', '复旦大学', '上海交通大学', '浙江大学', '华中科技大学', '西安交通大学']
    const departments = ['计算机学院', '软件学院', '信息学院', '机械学院', '管理学院', '经济学院', '法学院']
    const majors = ['计算机科学与技术', '软件工程', '电子信息', '机械工程', '工商管理', '经济学', '法学']

    const news = generateMockData.array(() => ({
      id: generateMockData.id(),
      title: `${newsTypes[generateMockData.number(0, newsTypes.length - 1)]} - ${generateMockData.name('标题')}`,
      content: `这是一条重要的研究生招生新闻内容，包含了详细的政策信息和相关要求。新闻编号：${generateMockData.number(1, 10000)}。内容详情请查看完整版本...`,
      summary: `${newsTypes[generateMockData.number(0, newsTypes.length - 1)]}相关的重要通知，请及时关注。`,
      created: generateMockData.datetime(-generateMockData.number(1, 30)),
      publishTime: generateMockData.datetime(-generateMockData.number(1, 30)),
      source: sources[generateMockData.number(0, sources.length - 1)],
      sourceUrl: `https://example.com/news/source/${generateMockData.id()}`,
      linkUrl: `https://example.com/news/${generateMockData.id()}`,
      unitCode: generateMockData.code('UNIT'),
      unitName: universities[generateMockData.number(0, universities.length - 1)],
      departmentCode: generateMockData.code('DEPT'),
      departmentName: departments[generateMockData.number(0, departments.length - 1)],
      majorCode: generateMockData.code('MAJOR'),
      majorName: majors[generateMockData.number(0, majors.length - 1)],
      degreeType: ['学术学位', '专业学位'][generateMockData.number(0, 1)],
      learnType: ['全日制', '非全日制'][generateMockData.number(0, 1)],
      isImportant: generateMockData.boolean(),
      isTop: generateMockData.boolean(),
      viewCount: generateMockData.number(10, 1000),
      tags: generateMockData.array(() => ['热门', '重要', '紧急', '推荐'][generateMockData.number(0, 3)], generateMockData.number(1, 3)),
    }), data?.pageSize || 10)

    return generateMockData.listResponse(news, 500, true)
  },
})
