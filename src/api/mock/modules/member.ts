/*
 * @Author: weish<PERSON>
 * @Date: 2025-08-17 16:00:00
 * @LastEditTime: 2025-08-17 16:23:37
 * @LastEditors: weisheng
 * @Description: 会员相关接口的mock数据
 * @FilePath: /tianxuan-mini/src/api/mock/modules/member.ts
 */
import { defineMock } from '@alova/mock'
import { generateMockData } from '../utils/generators'
import type {
  Baseresponse,
  Baseresponse1,
  Baseresponse2,
  Baseresponse_boolean,
  Baseresponse_int,
  Baseresponse_list_membercouponresult,
  Baseresponse_memberbuypayresult,
  Baseresponse_memberbuypreorderresult,
  Baseresponse_memberbuyprice,
  Baseresponse_memberrightsvo,
  Baseresponse_string,
  Baseresponse_void,
  Bebgdbjddb,
  Bgbfhjgacd,
  Code2SessionRequest,
  DecryptRequest,
  Gejdec,
  Kbgbgeebef,
  Kfaigihfae,
  Kfbhddgdhc,
  Kjceae<PERSON>bh,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Member<PERSON>uyPayResult,
  MemberBuyPreOrderResult,
  MemberBuyPrice,
  MemberCouponResult,
  MemberRightsVo,
} from '../../globals.d'

export default defineMock({
  // 头像更新
  '[POST]/xcx/member/avatar/update': ({ data }: { data: { avatar: string } }) => {
    console.log('[Mock] POST /xcx/member/avatar/update', data)
    const avatarUrl = `https://example.com/avatar/${generateMockData.code('AVATAR')}_${Date.now()}.jpg`
    return generateMockData.baseResponse(avatarUrl) as Baseresponse_string
  },

  // 支付H5页面
  '[GET]/xcx/member/buy/pay/h5': ({ query }: { query?: { orderId?: string } }) => {
    console.log('[Mock] GET /xcx/member/buy/pay/h5', query)
    const payUrl = `https://pay.example.com/h5?order=${query?.orderId || generateMockData.id()}&timestamp=${Date.now()}`
    return generateMockData.baseResponse(payUrl)
  },

  // 预创建支付订单
  '[POST]/xcx/member/buy/payPreCreate': ({ data }: { data: Kfbhddgdhc }) => {
    console.log('[Mock] POST /xcx/member/buy/payPreCreate', data)
    const orderId = generateMockData.id()
    const result: MemberBuyPreOrderResult = {
      orderId,
      orderNumber: `ORDER_${Date.now()}_${orderId}`,
      payInfo: JSON.stringify({
        appId: 'wx1234567890abcdef',
        timeStamp: Date.now().toString(),
        nonceStr: generateMockData.code('NONCE'),
        package: `prepay_id=${generateMockData.code('PREPAY')}`,
        signType: 'RSA',
        paySign: generateMockData.code('SIGN'),
      }),
    }
    return generateMockData.baseResponse(result) as Baseresponse_memberbuypreorderresult
  },

  // 查询支付结果
  '[POST]/xcx/member/buy/payQuery': ({ data }: { data: Kbgbgeebef }) => {
    console.log('[Mock] POST /xcx/member/buy/payQuery', data)
    const result: MemberBuyPayResult = {
      orderId: data?.orderId || generateMockData.id(),
      orderNumber: data?.orderNumber || `ORDER_${generateMockData.code('ORDER')}`,
      payState: generateMockData.number(0, 2), // 0成功 1失败 2支付中
    }
    return generateMockData.baseResponse(result) as Baseresponse_memberbuypayresult
  },

  // 获取会员价格
  '[POST]/xcx/member/buy/price': ({ data }: { data?: { couponCode?: string } }) => {
    console.log('[Mock] POST /xcx/member/buy/price', data)
    const result: MemberBuyPrice = {
      couponPrice: data?.couponCode ? 98.00 : 128.00, // 福利码优惠后价格
      rtlPrice: 128.00, // 标准价格
      virtualPrice: 198.00, // 虚拟原价
    }
    return generateMockData.baseResponse(result) as Baseresponse_memberbuyprice
  },

  // 是否可以弹窗
  '[GET]/xcx/member/canPopup': ({ query }: { query?: object }) => {
    console.log('[Mock] GET /xcx/member/canPopup', query)
    return generateMockData.baseResponse(generateMockData.boolean()) as Baseresponse_boolean
  },

  // 微信code2session
  '[POST]/xcx/member/code2Session': ({ data }: { data: Code2SessionRequest }) => {
    console.log('[Mock] POST /xcx/member/code2Session', data)

    if (!data?.code) {
      return {
        code: 400,
        message: '授权码不能为空',
        data: undefined,
        success: false,
      } as Baseresponse1
    }

    if (data.code === 'invalid_code') {
      return {
        code: 401,
        message: '授权码无效或已过期',
        data: undefined,
        success: false,
      } as Baseresponse1
    }

    const wxUserInfo: Kfaigihfae = {
      openid: `openid_${generateMockData.code('WX')}`,
      sessionKey: `session_${generateMockData.code('KEY')}`,
      unionid: `unionid_${generateMockData.code('WX')}`,
    }
    return generateMockData.baseResponse(wxUserInfo) as Baseresponse1
  },

  // 检查福利码
  '[POST]/xcx/member/coupon/check': ({ data }: { data: Bgbfhjgacd }) => {
    console.log('[Mock] POST /xcx/member/coupon/check', data)

    // 模拟福利码验证
    const validCoupons = ['WELCOME2024', 'STUDENT50', 'NEWUSER']
    const isValid = validCoupons.includes(data?.couponCode || '')

    return generateMockData.baseResponse(isValid) as Baseresponse_boolean
  },

  // 添加优惠券
  '[POST]/xcx/member/coupon/add': ({ data }: { data: { couponCode: string } }) => {
    console.log('[Mock] POST /xcx/member/coupon/add', data)
    const coupon: MemberCouponResult = {
      couponCode: data?.couponCode || generateMockData.code('COUPON'),
      created: generateMockData.datetime(),
      memberId: `member_${generateMockData.id()}`,
    }
    return generateMockData.baseResponse(coupon)
  },

  // 获取福利码列表
  '[POST]/xcx/member/coupon/list': ({ data }: { data?: Bebgdbjddb }) => {
    console.log('[Mock] POST /xcx/member/coupon/list', data)

    const coupons: MemberCouponResult[] = generateMockData.array(() => ({
      couponCode: `COUPON${generateMockData.code('COUP')}`,
      created: generateMockData.datetime(),
      memberId: generateMockData.id().toString(),
    }), 3)

    return generateMockData.baseResponse(coupons) as Baseresponse_list_membercouponresult
  },

  // 解密数据
  '[POST]/xcx/member/decrypt': ({ data }: { data: DecryptRequest }) => {
    console.log('[Mock] POST /xcx/member/decrypt', data)

    // 模拟解密结果 - 返回解密后的数据数量
    const decryptedCount = 3 // 解密了3个字段：手机号、姓名、身份证

    return generateMockData.baseResponse(decryptedCount) as Baseresponse_int
  },

  // 解密微信数据
  '[POST]/xcx/member/decryptWx': ({ data }: { data: DecryptRequest }) => {
    console.log('[Mock] POST /xcx/member/decryptWx', data)
    const decryptedData = {
      nickName: `用户${generateMockData.number(1000, 9999)}`,
      avatarUrl: `https://example.com/avatar/${generateMockData.code('AVATAR')}.jpg`,
      gender: generateMockData.number(0, 2), // 0未知 1男 2女
      country: '中国',
      province: ['北京', '上海', '广东', '浙江', '江苏', '湖北', '陕西', '四川'][generateMockData.number(0, 7)],
      city: ['北京', '上海', '广州', '杭州', '南京', '武汉', '西安', '成都'][generateMockData.number(0, 7)],
      language: 'zh_CN',
    }
    return generateMockData.baseResponse(decryptedData)
  },

  // 免费使用次数
  '[GET]/xcx/member/freeuage/count': ({ query }: { query?: object }) => {
    console.log('[Mock] GET /xcx/member/freeuage/count', query)
    return generateMockData.baseResponse(generateMockData.number(0, 10)) as Baseresponse_int
  },

  // 获取会员信息
  '[POST]/xcx/member/get': ({ data }: { data?: Gejdec }) => {
    console.log('[Mock] POST /xcx/member/get', data)

    const memberInfo: Gejdec = {
      avatar: 'https://example.com/avatar.jpg',
      biz1Code: '301',
      biz1Name: '数学一',
      biz1Score: 85,
      biz2Code: '408',
      biz2Name: '计算机学科专业基础',
      biz2Score: 120,
      departmentCode: '001',
      departmentName: '计算机科学与技术学院',
      directionCode: '081200',
      directionName: '计算机科学与技术',
      examRegistrationNumber: '123456789012345',
    }

    return generateMockData.baseResponse(memberInfo) as Baseresponse2
  },

  // 会员信息
  '[POST]/xcx/member/info': ({ data }: { data?: object }) => {
    console.log('[Mock] POST /xcx/member/info', data)
    const memberInfo: Gejdec = {
      id: `member_${generateMockData.id()}`,
      openid: `openid_${generateMockData.code('WX')}`,
      nickName: `考生${generateMockData.number(1000, 9999)}`,
      mobile: `1${generateMockData.number(3000000000, 9999999999)}`,
      avatar: `https://example.com/avatar/${generateMockData.code('AVATAR')}.jpg`,
      name: generateMockData.name('考生'),
      examRegistrationNumber: `${generateMockData.number(100000000000, 999999999999)}`,
      firstChoiceSchoolCode: generateMockData.code('SCHOOL'),
      firstChoiceSchoolName: `${['北京', '清华', '复旦', '上海交通', '浙江'][generateMockData.number(0, 4)]}大学`,
      firstChoiceDegreeCode: generateMockData.code('DEGREE'),
      firstChoiceDegreeName: `${['计算机科学与技术', '软件工程', '电子信息', '机械工程', '工商管理'][generateMockData.number(0, 4)]}`,
      departmentCode: generateMockData.code('DEPT'),
      departmentName: `${['计算机学院', '软件学院', '信息学院', '机械学院', '管理学院'][generateMockData.number(0, 4)]}`,
      directionCode: generateMockData.code('DIR'),
      directionName: `${['人工智能', '大数据', '网络安全', '软件开发', '系统架构'][generateMockData.number(0, 4)]}`,
      firstScore: generateMockData.number(300, 500),
      politicsScore: generateMockData.number(60, 100),
      foreignLanguageScore: generateMockData.number(60, 100),
      biz1Score: generateMockData.number(60, 150),
      biz2Score: generateMockData.number(60, 150),
      studyType: generateMockData.boolean() ? '1' : '0', // 0非全日制 1全日制
    }
    return generateMockData.baseResponse(memberInfo)
  },

  // 会员登录
  '[POST]/xcx/member/login': ({ data }: { data: { openid: string } }) => {
    console.log('[Mock] POST /xcx/member/login', data)
    const member = {
      id: `member_${generateMockData.id()}`,
      name: `考生${generateMockData.number(1000, 9999)}`,
      nickName: `用户${generateMockData.number(1000, 9999)}`,
      mobile: `138${generateMockData.number(10000000, 99999999)}`,
      avatar: `https://example.com/avatar/${generateMockData.code('AVATAR')}.jpg`,
      openid: data?.openid || `openid_${generateMockData.code('WX')}`,
      unionid: `unionid_${generateMockData.code('WX')}`,
      type: generateMockData.number(0, 1), // 0普通用户 1付费用户
      token: `token_${generateMockData.code('TOKEN')}_${Date.now()}`,
    }
    return generateMockData.baseResponse({ member })
  },

  // 更新昵称
  '[POST]/xcx/member/nickname/update': ({ data }: { data: { nickName: string } }) => {
    console.log('[Mock] POST /xcx/member/nickname/update', data)
    return generateMockData.baseResponse(null, 2000, '昵称更新成功') as Baseresponse_void
  },

  // 更新个人资料
  '[POST]/xcx/member/profile/update': ({ data }: { data: Gejdec }) => {
    console.log('[Mock] POST /xcx/member/profile/update', data)
    return generateMockData.baseResponse(null, 2000, '个人资料更新成功') as Baseresponse_void
  },

  // 记录弹窗次数
  '[POST]/xcx/member/recordPopTimes': ({ data }: { data?: object }) => {
    console.log('[Mock] POST /xcx/member/recordPopTimes', data)
    return generateMockData.baseResponse(null, 2000, '记录成功') as Baseresponse_void
  },

  // 会员注册
  '[POST]/xcx/member/register': ({ data }: { data: Gejdec }) => {
    console.log('[Mock] POST /xcx/member/register', data)
    return generateMockData.baseResponse(null, 2000, '注册成功') as Baseresponse_void
  },

  // 剩余更新次数
  '[GET]/xcx/member/remainUpdateTimes': ({ query }: { query?: object }) => {
    console.log('[Mock] GET /xcx/member/remainUpdateTimes', query)
    return generateMockData.baseResponse(generateMockData.number(0, 5)) as Baseresponse_int
  },

  // 会员权益
  '[POST]/xcx/member/rights': ({ data }: { data?: object }) => {
    console.log('[Mock] POST /xcx/member/rights', data)
    const rights: MemberRightsVo = {
      bannerPics: generateMockData.array(() => ({
        masterPic: `https://example.com/banner/${generateMockData.code('BANNER')}.jpg`,
        childrenPics: generateMockData.array(() => `https://example.com/banner/child/${generateMockData.code('CHILD')}.jpg`, generateMockData.number(0, 3)),
      } as MasterSlavePic), 3),
      customerServiceQrcode: `https://example.com/qrcode/${generateMockData.code('QR')}.jpg`,
      popupPic: {
        masterPic: `https://example.com/popup/${generateMockData.code('POPUP')}.jpg`,
        childrenPics: [],
      } as MasterSlavePic,
      rightsPic: `https://example.com/rights/${generateMockData.code('RIGHTS')}.jpg`,
      userGuide: `https://example.com/guide/${generateMockData.code('GUIDE')}.pdf`,
    }
    return generateMockData.baseResponse(rights) as Baseresponse_memberrightsvo
  },

  // 启动过期任务
  '[POST]/xcx/member/startExpireJob': ({ data }: { data?: object }) => {
    console.log('[Mock] POST /xcx/member/startExpireJob', data)
    return generateMockData.baseResponse(null, 2000, '任务启动成功') as Baseresponse_void
  },

  // 更新个人资料（新增接口）
  '[POST]/xcx/member/updateProfile': ({ data }: { data: Kjceaejjbh }) => {
    console.log('[Mock] POST /xcx/member/updateProfile', data)
    return generateMockData.baseResponse(null, 2000, '个人资料更新成功') as Baseresponse_void
  },

  // 会员注册（新增接口）
  '[POST]/xcx/member/registerMember': ({ data }: { data: Kcaefjaecja }) => {
    console.log('[Mock] POST /xcx/member/registerMember', data)
    const memberResult = {
      id: generateMockData.id(),
      mobile: data.mobile,
      nickName: data.nickName,
      openid: data.openid,
      unionid: data.unionid,
      registeredAt: generateMockData.datetime(),
    }
    return generateMockData.baseResponse(memberResult)
  },
})
