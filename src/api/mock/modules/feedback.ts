/*
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-08-17 16:00:00
 * @LastEditTime: 2025-08-17 16:15:29
 * @LastEditors: weisheng
 * @Description: 反馈相关接口的mock数据
 * @FilePath: /tianxuan-mini/src/api/mock/modules/feedback.ts
 */
import { defineMock } from '@alova/mock'
import { generateMockData } from '../utils/generators'
import type { Baseresponse_list_feedbackvo, Baseresponse_void, FeedBackFilter, FeedBackRequest, FeedBackVo } from '../../globals.d'

export default defineMock({
  // 创建反馈
  '[POST]/xcx/feedback/create': ({ data }: { data: FeedBackRequest }) => {
    console.log('[Mock] POST /xcx/feedback/create', data)

    // 模拟表单验证
    if (!data?.content) {
      return {
        code: 4000,
        data: null,
        msg: '反馈内容不能为空',
      } as Baseresponse_void
    }

    return generateMockData.baseResponse(null, 2000, '反馈提交成功，我们会尽快处理您的问题') as Baseresponse_void
  },

  // 反馈列表
  '[POST]/xcx/feedback/list': ({ data }: { data: FeedBackFilter }) => {
    console.log('[Mock] POST /xcx/feedback/list', data)

    const feedbacks: FeedBackVo[] = generateMockData.array(() => ({
      contactInfo: `user${generateMockData.number(1000, 9999)}@example.com`,
      content: `这是一条用户反馈内容，描述了遇到的问题或建议。内容编号：${generateMockData.number(1, 1000)}`,
      created: generateMockData.datetime(-generateMockData.number(1, 30)),
      images: generateMockData.array(() => `https://example.com/feedback/image${generateMockData.number(1, 100)}.jpg`, generateMockData.number(0, 3)),
      type: (['BUG', 'CORRECT', 'ADVISE', 'COMPLAINT', 'OTHER'] as const)[generateMockData.number(0, 4)],
    }), data?.pageSize || 10)

    return generateMockData.listResponse(feedbacks, 50, true) as Baseresponse_list_feedbackvo
  },
})
