/*
 * @Author: weish<PERSON>
 * @Date: 2025-08-17 16:00:00
 * @LastEditTime: 2025-08-17 16:50:47
 * @LastEditors: weisheng
 * @Description: 文件相关接口的mock数据
 * @FilePath: /tianxuan-mini/src/api/mock/modules/file.ts
 */
import { defineMock } from '@alova/mock'
import { generateMockData } from '../utils/generators'
import type { Baseresponse_string } from '../../globals.d'

export default defineMock({
  // 文件上传
  '[POST]/xcx/upload': ({ data }) => {
    console.log('[Mock] POST /xcx/upload', data)

    // 模拟不同类型的文件上传
    const fileTypes = ['jpg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx']
    const fileType = fileTypes[generateMockData.number(0, fileTypes.length - 1)]

    // 模拟上传成功
    const uploadResult = {
      fileId: generateMockData.id(),
      fileName: `upload_${generateMockData.code('FILE')}.${fileType}`,
      fileUrl: `https://example.com/uploads/${generateMockData.code('FILE')}.${fileType}`,
      fileSize: generateMockData.number(1024, 10485760), // 1KB - 10MB
      uploadTime: generateMockData.datetime(),
      contentType: `${fileType === 'jpg' || fileType === 'png' || fileType === 'gif' ? 'image' : 'application'}/${fileType}`,
    }

    // 模拟上传失败的情况
    if (generateMockData.number(1, 100) <= 5) { // 5% 失败率
      return {
        code: 4000,
        data: null,
        msg: '文件上传失败，请重试',
      }
    }

    return generateMockData.baseResponse(uploadResult.fileUrl, 2000, '文件上传成功')
  },
})
