/*
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-08-17 16:00:00
 * @LastEditTime: 2025-08-17 16:50:59
 * @LastEditors: weisheng
 * @Description: 监控相关接口的mock数据
 * @FilePath: /tianxuan-mini/src/api/mock/modules/monitor.ts
 */
import { defineMock } from '@alova/mock'
import { generateMockData } from '../utils/generators'
import type { Baseresponse_string, Baseresponse_void } from '../../globals.d'

export default defineMock({
  // 清除缓存
  '[DELETE]/monitor/cache/clearCacheKey/{cacheKey}': (request) => {
    const cacheKey = request.params?.cacheKey || 'unknown'
    console.log('[Mock] DELETE /monitor/cache/clearCacheKey', cacheKey)
    return generateMockData.baseResponse(null, 2000, `缓存 ${cacheKey} 清除成功`) as Baseresponse_void
  },

  // 获取缓存值
  '[GET]/monitor/cache/getValue/{cacheName}/{cacheKey}': (request) => {
    const { cacheName, cacheKey } = request.params || {}
    console.log('[Mock] GET /monitor/cache/getValue', { cacheName, cacheKey })
    return generateMockData.baseResponse(`缓存值: ${cacheKey}_value`) as Baseresponse_string
  },
})
