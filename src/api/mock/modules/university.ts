/*
 * @Author: weish<PERSON>
 * @Date: 2025-08-17 16:00:00
 * @LastEditTime: 2025-08-17 16:50:27
 * @LastEditors: weisheng
 * @Description: 大学相关接口的mock数据
 * @FilePath: /tianxuan-mini/src/api/mock/modules/university.ts
 */
import { defineMock } from '@alova/mock'
import { generateMockData } from '../utils/generators'
import type {
  AdjustAnalysisResultDetailVo,
  AdjustAnalysisResultVo,
  AdmissionBaseFilter,
  AdmissionBaseVo,
  AdmissionItem,
  Baseresponse_adjustanalysisresultvo,
  Baseresponse_admissionbasevo,
  Baseresponse_admissionproposedname4analysisvo,
  Baseresponse_compareresultvo,
  Baseresponse_list_admissionlast3yearsscorerangevo,
  Baseresponse_list_admissionlast3yearsvo,
  Baseresponse_list_admissionmajorcatalogvo,
  Baseresponse_list_codenamevo,
  Baseresponse_void,
  ExamVo,
  ReferBookVo,
  SecondCourseVo,
} from '../../globals.d'

// 常量定义
const universities = ['北京大学', '清华大学', '复旦大学', '上海交通大学', '浙江大学', '华中科技大学', '西安交通大学', '哈尔滨工业大学', '中南大学', '东南大学']
const majors = ['计算机科学与技术', '软件工程', '电子信息', '机械工程', '工商管理', '经济学', '法学', '数学', '物理学', '化学']
const subjects = ['工学', '理学', '管理学', '经济学', '文学', '法学', '医学', '教育学']

export default defineMock({
  // 调剂分析结果
  '[POST]/xcx/university/adjust/analysis/result': ({ data }) => {
    console.log('[Mock] POST /xcx/university/adjust/analysis/result', data)

    const details = generateMockData.array(() => ({
      uniCode: generateMockData.code('UNI'),
      uniName: universities[generateMockData.number(0, universities.length - 1)],
      majorCode: generateMockData.code('MAJOR'),
      majorName: majors[generateMockData.number(0, majors.length - 1)],
      degreeType: ['学术学位', '专业学位'][generateMockData.number(0, 1)],
      learnType: ['全日制', '非全日制'][generateMockData.number(0, 1)],
      props: generateMockData.array(() => ['985', '211', '双一流'][generateMockData.number(0, 2)], generateMockData.number(1, 3)),
      provinceType: ['A', 'B'][generateMockData.number(0, 1)],
      subjectCategoryCode: generateMockData.code('CATEGORY'),
      subjectCategoryName: subjects[generateMockData.number(0, subjects.length - 1)],
      oneSubjectCode: generateMockData.code('SUBJECT'),
      oneSubjectName: majors[generateMockData.number(0, majors.length - 1)],
    }), 20)

    return generateMockData.baseResponse({
      adjustUniTotal: generateMockData.number(50, 200),
      candidateType: ['应届', '往届'][generateMockData.number(0, 1)],
      details,
      detailsA: details.filter(() => generateMockData.boolean()),
      detailsB: details.filter(() => generateMockData.boolean()),
      gteA: generateMockData.boolean(),
      sameTypeUniTotal: generateMockData.number(20, 100),
    })
  },

  // 同分去向分析
  '[POST]/xcx/university/adjust/analysis/sameScore/dest': ({ data }) => {
    console.log('[Mock] POST /xcx/university/adjust/analysis/sameScore/dest', data)
    const items = generateMockData.array(index => ({
      year: `${2020 + index}`,
      details: generateMockData.array(() => ({
        prop: ['985', '211', '双一流', '普通'][generateMockData.number(0, 3)],
        count: generateMockData.number(1, 50),
      }), 4),
    }), 3)

    return generateMockData.baseResponse({ items })
  },

  // 同校去向分析
  '[POST]/xcx/university/adjust/analysis/sameUni/dest': ({ data }) => {
    console.log('[Mock] POST /xcx/university/adjust/analysis/sameUni/dest', data)
    const items = generateMockData.array(index => ({
      year: `${2020 + index}`,
      details: generateMockData.array(() => ({
        prop: ['985', '211', '双一流', '普通'][generateMockData.number(0, 3)],
        count: generateMockData.number(1, 50),
      }), 4),
    }), 3)

    return generateMockData.baseResponse({ items })
  },

  // 招生基础信息
  '[POST]/xcx/university/admission/base': ({ data }) => {
    console.log('[Mock] POST /xcx/university/admission/base', data)
    const admissionItems = generateMockData.array(() => ({
      degreeType: ['学术学位', '专业学位'][generateMockData.number(0, 1)],
      directionCode: generateMockData.code('DIRECTION'),
      directionName: `${['人工智能', '大数据', '网络安全', '软件开发', '系统架构'][generateMockData.number(0, 4)]}`,
      examList: generateMockData.array(() => ({
        politicsCode: generateMockData.code('POLITICS'),
        politicsName: '思想政治理论',
        foreignLanguageCode: generateMockData.code('FOREIGN'),
        foreignLanguageName: ['英语一', '英语二', '日语', '俄语'][generateMockData.number(0, 3)],
        bizCourseOneCode: generateMockData.code('BIZ1'),
        bizCourseOneName: `${['数学一', '数学二', '数学三', '经济类联考'][generateMockData.number(0, 3)]}`,
        bizCourseTwoCode: generateMockData.code('BIZ2'),
        bizCourseTwoName: `${['计算机专业基础', '软件工程基础', '管理学原理', '经济学原理'][generateMockData.number(0, 3)]}`,
      }), 1),
      guideTeacher: `${['张', '李', '王', '刘', '陈'][generateMockData.number(0, 4)]}教授`,
      learnType: ['全日制', '非全日制'][generateMockData.number(0, 1)],
      minorityCorePlan: generateMockData.boolean(),
      proposedAdmissionNum: generateMockData.number(5, 50),
      referBook: {
        name: `${['计算机网络', '数据结构', '操作系统', '数据库原理', '软件工程'][generateMockData.number(0, 4)]}`,
        author: `${['谢希仁', '严蔚敏', '汤小丹', '萨师煊', '张海藩'][generateMockData.number(0, 4)]}`,
        publishHouse: `${['清华大学出版社', '高等教育出版社', '机械工业出版社', '电子工业出版社'][generateMockData.number(0, 3)]}`,
        year: `${2018 + generateMockData.number(0, 6)}`,
      },
      retirePlan: generateMockData.boolean(),
      secondCourseList: generateMockData.array(() => ({
        code: generateMockData.code('SECOND'),
        name: `${['专业综合', '专业面试', '英语口语', '专业实践'][generateMockData.number(0, 3)]}`,
      }), generateMockData.number(1, 3)),
    }), generateMockData.number(3, 10))

    return generateMockData.baseResponse({ admissionItems })
  },

  // 近3年录取详情
  '[POST]/xcx/university/admission/last3Years': ({ data }) => {
    console.log('[Mock] POST /xcx/university/admission/last3Years', data)
    const last3Years = generateMockData.array(index => ({
      year: `${2021 + index}`,
      total: generateMockData.number(20, 100),
      oneVolunteer: generateMockData.number(10, 80),
      adjust: generateMockData.number(0, 20),
    }), 3)

    return generateMockData.listResponse(last3Years)
  },

  // 近3年分数范围
  '[POST]/xcx/university/admission/last3YearsScoreRange': ({ data }) => {
    console.log('[Mock] POST /xcx/university/admission/last3YearsScoreRange', data)
    const scoreRanges = generateMockData.array(index => ({
      year: `${2021 + index}`,
      firstChoiceMin: generateMockData.number(300, 380),
      firstChoiceMax: generateMockData.number(380, 450),
      adjustMin: generateMockData.number(280, 350),
      adjustMax: generateMockData.number(350, 420),
    }), 3)

    return generateMockData.listResponse(scoreRanges)
  },

  // 拟录取名单
  '[POST]/xcx/university/admission/proposed/name': ({ data }) => {
    console.log('[Mock] POST /xcx/university/admission/proposed/name', data)
    const proposedNames = generateMockData.array(() => ({
      name: `考生${generateMockData.number(1000, 9999)}`,
      admissionType: ['一志愿', '调剂'][generateMockData.number(0, 1)],
      first: generateMockData.number(300, 450),
      firstRank: generateMockData.number(1, 100),
      politics: generateMockData.number(60, 85),
      foreign: generateMockData.number(60, 85),
      bizOne: generateMockData.number(60, 150),
      bizTwo: generateMockData.number(60, 150),
      oneVolunteerUniCode: generateMockData.code('UNI'),
      oneVolunteerUniName: universities[generateMockData.number(0, universities.length - 1)],
    }), data?.pageSize || 20)

    return generateMockData.listResponse(proposedNames, 100, true)
  },

  // 招生专业目录查询
  '[POST]/xcx/university/admission_major_catalog/query': ({ data }) => {
    console.log('[Mock] POST /xcx/university/admission_major_catalog/query', data)
    const catalogs = generateMockData.array(() => ({
      uniCode: generateMockData.code('UNI'),
      uniName: universities[generateMockData.number(0, universities.length - 1)],
      majorCode: generateMockData.code('MAJOR'),
      majorName: majors[generateMockData.number(0, majors.length - 1)],
      degreeType: ['学术学位', '专业学位'][generateMockData.number(0, 1)],
      learnType: ['全日制', '非全日制'][generateMockData.number(0, 1)],
      proposedAdmissionNum: generateMockData.number(5, 50),
    }), data?.pageSize || 20)

    return generateMockData.listResponse(catalogs, 200, true)
  },

  // 院校代码名称列表
  '[POST]/xcx/university/codeName/list': ({ data }) => {
    console.log('[Mock] POST /xcx/university/codeName/list', data)
    const codeNames = generateMockData.array(() => ({
      code: generateMockData.code('UNI'),
      name: universities[generateMockData.number(0, universities.length - 1)],
    }), data?.pageSize || 50)

    return generateMockData.listResponse(codeNames, 500, true)
  },

  // 院校对比
  '[POST]/xcx/university/compare': ({ data }) => {
    console.log('[Mock] POST /xcx/university/compare', data)
    const compareData = generateMockData.array(() => ({
      uniCode: generateMockData.code('UNI'),
      uniName: universities[generateMockData.number(0, universities.length - 1)],
      props: generateMockData.array(() => ['985', '211', '双一流'][generateMockData.number(0, 2)], generateMockData.number(1, 3)),
      location: ['北京', '上海', '杭州', '南京', '广州'][generateMockData.number(0, 4)],
      establishedYear: generateMockData.number(1900, 2000),
      studentCount: generateMockData.number(10000, 50000),
      teacherCount: generateMockData.number(1000, 5000),
      academicianCount: generateMockData.number(5, 50),
      nationalKeyDisciplines: generateMockData.number(10, 100),
    }), 2)

    return generateMockData.baseResponse(compareData)
  },

  // 院系查询（用于注册）
  '[POST]/xcx/university/department/queryForRegister': ({ data }) => {
    console.log('[Mock] POST /xcx/university/department/queryForRegister', data)
    const departments = generateMockData.array(() => ({
      code: generateMockData.code('DEPT'),
      name: `${['计算机学院', '软件学院', '信息学院', '机械学院', '管理学院', '经济学院', '法学院', '文学院', '理学院', '医学院'][generateMockData.number(0, 9)]}`,
    }), generateMockData.number(5, 20))

    return generateMockData.listResponse(departments)
  },

  // 研究方向查询（用于注册）
  '[POST]/xcx/university/direction/queryForRegister': ({ data }) => {
    console.log('[Mock] POST /xcx/university/direction/queryForRegister', data)
    const directions = generateMockData.array(() => ({
      code: generateMockData.code('DIRECTION'),
      name: `${['人工智能', '大数据', '网络安全', '软件开发', '系统架构', '机器学习', '计算机视觉', '自然语言处理', '分布式系统', '云计算'][generateMockData.number(0, 9)]}`,
    }), generateMockData.number(3, 15))

    return generateMockData.listResponse(directions)
  },

  // 收藏相关
  '[POST]/xcx/university/fav/collect': ({ data }) => {
    console.log('[Mock] POST /xcx/university/fav/collect', data)
    return generateMockData.baseResponse(null, 2000, '收藏成功')
  },

  '[POST]/xcx/university/fav/list': ({ data }) => {
    console.log('[Mock] POST /xcx/university/fav/list', data)
    const favList = generateMockData.array(() => ({
      id: generateMockData.id(),
      uniCode: generateMockData.code('UNI'),
      uniName: universities[generateMockData.number(0, universities.length - 1)],
      majorCode: generateMockData.code('MAJOR'),
      majorName: majors[generateMockData.number(0, majors.length - 1)],
      collectTime: generateMockData.datetime(-generateMockData.number(1, 30)),
      isSubscribed: generateMockData.boolean(),
    }), data?.pageSize || 10)

    return generateMockData.listResponse(favList, 50, true)
  },

  '[POST]/xcx/university/fav/subscribe': ({ data }) => {
    console.log('[Mock] POST /xcx/university/fav/subscribe', data)
    return generateMockData.baseResponse(null, 2000, '订阅成功')
  },

  '[POST]/xcx/university/fav/unCollect': ({ data }) => {
    console.log('[Mock] POST /xcx/university/fav/unCollect', data)
    return generateMockData.baseResponse(null, 2000, '取消收藏成功')
  },

  '[POST]/xcx/university/fav/unSubscribe': ({ data }) => {
    console.log('[Mock] POST /xcx/university/fav/unSubscribe', data)
    return generateMockData.baseResponse(null, 2000, '取消订阅成功')
  },

  // 竞争分析相关
  '[POST]/xcx/university/fightAnalysis/myRank': ({ data }) => {
    console.log('[Mock] POST /xcx/university/fightAnalysis/myRank', data)
    return generateMockData.baseResponse({
      rank: generateMockData.number(1, 1000),
      totalCount: generateMockData.number(1000, 5000),
      percentage: generateMockData.number(1, 100),
      scoreRange: {
        min: generateMockData.number(300, 380),
        max: generateMockData.number(380, 450),
      },
    })
  },

  '[POST]/xcx/university/fightAnalysis/oneScoreOneRange': ({ data }) => {
    console.log('[Mock] POST /xcx/university/fightAnalysis/oneScoreOneRange', data)
    const scoreRanges = generateMockData.array(() => ({
      score: generateMockData.number(300, 450),
      rank: generateMockData.number(1, 1000),
      count: generateMockData.number(10, 100),
    }), 20)

    return generateMockData.listResponse(scoreRanges)
  },

  '[POST]/xcx/university/fightAnalysis/sameScore/dest': ({ data }) => {
    console.log('[Mock] POST /xcx/university/fightAnalysis/sameScore/dest', data)
    const destinations = generateMockData.array(() => ({
      uniCode: generateMockData.code('UNI'),
      uniName: universities[generateMockData.number(0, universities.length - 1)],
      majorCode: generateMockData.code('MAJOR'),
      majorName: majors[generateMockData.number(0, majors.length - 1)],
      count: generateMockData.number(1, 20),
      percentage: generateMockData.number(1, 100),
    }), data?.pageSize || 20)

    return generateMockData.listResponse(destinations, 100, true)
  },

  '[POST]/xcx/university/fightAnalysis/sameScore/dest/statistics': ({ data }) => {
    console.log('[Mock] POST /xcx/university/fightAnalysis/sameScore/dest/statistics', data)
    return generateMockData.baseResponse({
      totalCount: generateMockData.number(100, 1000),
      admittedCount: generateMockData.number(50, 500),
      admissionRate: generateMockData.number(30, 80),
      averageScore: generateMockData.number(350, 420),
    })
  },

  '[POST]/xcx/university/fightAnalysis/sameUni/dest': ({ data }) => {
    console.log('[Mock] POST /xcx/university/fightAnalysis/sameUni/dest', data)
    const destinations = generateMockData.array(() => ({
      majorCode: generateMockData.code('MAJOR'),
      majorName: majors[generateMockData.number(0, majors.length - 1)],
      count: generateMockData.number(1, 20),
      percentage: generateMockData.number(1, 100),
      avgScore: generateMockData.number(350, 420),
    }), data?.pageSize || 20)

    return generateMockData.listResponse(destinations, 50, true)
  },

  '[POST]/xcx/university/fightAnalysis/sameUni/dest/statistics': ({ data }) => {
    console.log('[Mock] POST /xcx/university/fightAnalysis/sameUni/dest/statistics', data)
    return generateMockData.baseResponse({
      totalMajors: generateMockData.number(20, 100),
      totalStudents: generateMockData.number(500, 2000),
      averageAdmissionScore: generateMockData.number(350, 420),
      competitionIntensity: generateMockData.number(1, 10),
    })
  },

  // 一志愿分析
  '[POST]/xcx/university/firstChoice/analysis/year': ({ data }) => {
    console.log('[Mock] POST /xcx/university/firstChoice/analysis/year', data)
    const yearAnalysis = generateMockData.array(index => ({
      year: `${2020 + index}`,
      applicantCount: generateMockData.number(100, 1000),
      admittedCount: generateMockData.number(20, 200),
      admissionRate: generateMockData.number(10, 50),
      minScore: generateMockData.number(300, 380),
      maxScore: generateMockData.number(380, 450),
      avgScore: generateMockData.number(340, 410),
    }), 4)

    return generateMockData.listResponse(yearAnalysis)
  },

  '[POST]/xcx/university/firstChoice/predict': ({ data }) => {
    console.log('[Mock] POST /xcx/university/firstChoice/predict', data)
    return generateMockData.baseResponse({
      predictedScore: generateMockData.number(350, 420),
      admissionProbability: generateMockData.number(20, 90),
      riskLevel: ['低', '中', '高'][generateMockData.number(0, 2)],
      recommendation: '建议作为冲刺院校',
      similarCases: generateMockData.number(10, 100),
    })
  },

  // 院校层次
  '[GET]/xcx/university/levels': ({ query }) => {
    console.log('[Mock] GET /xcx/university/levels', query)
    return generateMockData.baseResponse([
      { code: '985', name: '985工程' },
      { code: '211', name: '211工程' },
      { code: 'DOUBLE_FIRST_CLASS', name: '双一流' },
      { code: 'ORDINARY', name: '普通院校' },
    ])
  },

  // 专业详情
  '[POST]/xcx/university/major/detail': ({ data }) => {
    console.log('[Mock] POST /xcx/university/major/detail', data)
    return generateMockData.baseResponse({
      majorCode: data?.majorCode || generateMockData.code('MAJOR'),
      majorName: majors[generateMockData.number(0, majors.length - 1)],
      description: '这是一个专业的详细描述，包含了专业的培养目标、主要课程、就业方向等信息...',
      degreeType: ['学术学位', '专业学位'][generateMockData.number(0, 1)],
      studyYears: generateMockData.number(2, 4),
      tuitionFee: generateMockData.number(8000, 50000),
      employmentRate: generateMockData.number(85, 98),
      averageSalary: generateMockData.number(8000, 25000),
    })
  },

  // 专业查询（用于注册）
  '[POST]/xcx/university/major/queryForRegister': ({ data }) => {
    console.log('[Mock] POST /xcx/university/major/queryForRegister', data)
    const majorList = generateMockData.array(() => ({
      code: generateMockData.code('MAJOR'),
      name: majors[generateMockData.number(0, majors.length - 1)],
    }), generateMockData.number(10, 50))

    return generateMockData.listResponse(majorList)
  },

  // 查询条件列表
  '[POST]/xcx/university/qd/list': ({ data }) => {
    console.log('[Mock] POST /xcx/university/qd/list', data)
    const conditions = {
      provinces: generateMockData.array(() => ({
        code: generateMockData.code('PROVINCE'),
        name: ['北京', '上海', '广东', '浙江', '江苏', '湖北', '陕西', '四川', '山东', '河南'][generateMockData.number(0, 9)],
      }), 10),
      uniTypes: [
        { code: '985', name: '985工程' },
        { code: '211', name: '211工程' },
        { code: 'DOUBLE_FIRST_CLASS', name: '双一流' },
        { code: 'ORDINARY', name: '普通院校' },
      ],
      degreeTypes: [
        { code: 'ACADEMIC', name: '学术学位' },
        { code: 'PROFESSIONAL', name: '专业学位' },
      ],
      learnTypes: [
        { code: 'FULL_TIME', name: '全日制' },
        { code: 'PART_TIME', name: '非全日制' },
      ],
    }

    return generateMockData.baseResponse(conditions)
  },

  // 按专业查询院校
  '[POST]/xcx/university/queryByMajor': ({ data }) => {
    console.log('[Mock] POST /xcx/university/queryByMajor', data)
    const universityList = generateMockData.array(() => ({
      uniCode: generateMockData.code('UNI'),
      uniName: universities[generateMockData.number(0, universities.length - 1)],
      majorCode: generateMockData.code('MAJOR'),
      majorName: majors[generateMockData.number(0, majors.length - 1)],
      props: generateMockData.array(() => ['985', '211', '双一流'][generateMockData.number(0, 2)], generateMockData.number(1, 3)),
      location: ['北京', '上海', '杭州', '南京', '广州', '武汉', '西安', '哈尔滨', '长沙', '南京'][generateMockData.number(0, 9)],
      admissionScore: generateMockData.number(300, 450),
      admissionRank: generateMockData.number(1, 1000),
    }), data?.pageSize || 20)

    return generateMockData.listResponse(universityList, 500, true)
  },

  // 按院校查询专业
  '[POST]/xcx/university/queryByUni': ({ data }) => {
    console.log('[Mock] POST /xcx/university/queryByUni', data)
    const majorList = generateMockData.array(() => ({
      majorCode: generateMockData.code('MAJOR'),
      majorName: majors[generateMockData.number(0, majors.length - 1)],
      degreeType: ['学术学位', '专业学位'][generateMockData.number(0, 1)],
      learnType: ['全日制', '非全日制'][generateMockData.number(0, 1)],
      proposedAdmissionNum: generateMockData.number(5, 50),
      admissionScore: generateMockData.number(300, 450),
      admissionRank: generateMockData.number(1, 1000),
    }), data?.pageSize || 20)

    return generateMockData.listResponse(majorList, 100, true)
  },

  // 院校查询（用于注册）
  '[POST]/xcx/university/queryForRegister': ({ data }) => {
    console.log('[Mock] POST /xcx/university/queryForRegister', data)
    const universityList = generateMockData.array(() => ({
      code: generateMockData.code('UNI'),
      name: universities[generateMockData.number(0, universities.length - 1)],
    }), data?.pageSize || 50)

    return generateMockData.listResponse(universityList, 1000, true)
  },

  // 院校专业查询
  '[POST]/xcx/university/queryUniMajor': ({ data }) => {
    console.log('[Mock] POST /xcx/university/queryUniMajor', data)
    const uniMajors = generateMockData.array(() => ({
      uniCode: generateMockData.code('UNI'),
      uniName: universities[generateMockData.number(0, universities.length - 1)],
      majorCode: generateMockData.code('MAJOR'),
      majorName: majors[generateMockData.number(0, majors.length - 1)],
      degreeType: ['学术学位', '专业学位'][generateMockData.number(0, 1)],
      learnType: ['全日制', '非全日制'][generateMockData.number(0, 1)],
      props: generateMockData.array(() => ['985', '211', '双一流'][generateMockData.number(0, 2)], generateMockData.number(1, 3)),
      location: ['北京', '上海', '杭州', '南京', '广州'][generateMockData.number(0, 4)],
    }), data?.pageSize || 20)

    return generateMockData.listResponse(uniMajors, 1000, true)
  },

  // 分数图表
  '[POST]/xcx/university/scoreChart': ({ data }) => {
    console.log('[Mock] POST /xcx/university/scoreChart', data)
    const chartData = generateMockData.array(index => ({
      year: `${2020 + index}`,
      minScore: generateMockData.number(300, 380),
      maxScore: generateMockData.number(380, 450),
      avgScore: generateMockData.number(340, 410),
      nationalLine: generateMockData.number(280, 350),
      admissionCount: generateMockData.number(20, 100),
    }), 4)

    return generateMockData.baseResponse(chartData)
  },

  // 服务器近3年数据
  '[GET]/xcx/university/serverLast3Year': ({ query }) => {
    console.log('[Mock] GET /xcx/university/serverLast3Year', query)
    return generateMockData.baseResponse([
      { year: '2021', dataCount: generateMockData.number(10000, 50000) },
      { year: '2022', dataCount: generateMockData.number(10000, 50000) },
      { year: '2023', dataCount: generateMockData.number(10000, 50000) },
    ])
  },
})
