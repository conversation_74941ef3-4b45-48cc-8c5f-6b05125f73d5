/*
 * @Author: weish<PERSON>
 * @Date: 2025-08-17 16:00:00
 * @LastEditTime: 2025-08-17 16:51:23
 * @LastEditors: weisheng
 * @Description: 首选预测相关接口的mock数据
 * @FilePath: /tianxuan-mini/src/api/mock/modules/syb.ts
 */
import { defineMock } from '@alova/mock'
import { generateMockData } from '../utils/generators'
import type {
  Baseresponse,
  Baseresponse_sybfirstchoicepredictvo,
  Bcihadccia,
  Bcihbecgej,
  SybFirstChoicePredictAdmissionIntervalRequest,
  SybFirstChoicePredictAdmissionRequest,
  SybFirstChoicePredictVo,
} from '../../globals.d'

export default defineMock({
  // 首选预测线
  '[POST]/xcx/syb/firstChoice/predict': ({ data }) => {
    console.log('[Mock] POST /xcx/syb/firstChoice/predict', data)
    const predictResult = {
      predictScore: generateMockData.number(350, 420),
      confidence: generateMockData.number(70, 95),
      trend: ['上升', '稳定', '下降'][generateMockData.number(0, 2)],
      factors: generateMockData.array(() => ({
        factor: ['历年分数', '报考人数', '招生计划', '专业热度'][generateMockData.number(0, 3)],
        weight: generateMockData.number(10, 30),
        impact: ['正面', '负面', '中性'][generateMockData.number(0, 2)],
      }), 4),
    }
    return generateMockData.baseResponse(predictResult)
  },

  // 首选预测录取
  '[POST]/xcx/syb/firstChoice/predict/admission': ({ data }) => {
    console.log('[Mock] POST /xcx/syb/firstChoice/predict/admission', data)
    const admissionResult = {
      admissionProbability: generateMockData.number(20, 90),
      riskLevel: ['低风险', '中风险', '高风险'][generateMockData.number(0, 2)],
      recommendation: ['冲刺', '稳妥', '保底'][generateMockData.number(0, 2)],
      similarCases: generateMockData.number(10, 100),
      historicalData: generateMockData.array(index => ({
        year: `${2020 + index}`,
        admissionScore: generateMockData.number(300, 450),
        admissionCount: generateMockData.number(20, 100),
        applicantCount: generateMockData.number(100, 500),
      }), 4),
    }
    return generateMockData.baseResponse(admissionResult)
  },

  // 首选预测录取区间
  '[POST]/xcx/syb/firstChoice/predict/admission/interval': ({ data }) => {
    console.log('[Mock] POST /xcx/syb/firstChoice/predict/admission/interval', data)
    const intervalResult = {
      minScore: generateMockData.number(300, 380),
      maxScore: generateMockData.number(380, 450),
      avgScore: generateMockData.number(340, 410),
      intervals: generateMockData.array(() => ({
        scoreRange: `${generateMockData.number(300, 380)}-${generateMockData.number(380, 450)}`,
        probability: generateMockData.number(10, 90),
        candidateCount: generateMockData.number(50, 200),
      }), 5),
    }
    return generateMockData.baseResponse(intervalResult)
  },
})
