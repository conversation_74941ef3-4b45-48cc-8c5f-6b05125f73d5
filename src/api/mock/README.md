# API Mock 数据

本目录包含了项目中使用的 API 模拟数据，用于开发和测试环境。

## 目录结构

```
mock/
├── modules/                       # 按模块分类的模拟数据
│   ├── common.ts                 # 通用模拟处理（兜底处理）
│   ├── monitor.ts                # 监控相关接口的模拟数据
│   ├── syb.ts                    # 首选预测相关接口的模拟数据
│   ├── feedback.ts               # 反馈相关接口的模拟数据
│   ├── member.ts                 # 会员相关接口的模拟数据
│   ├── news.ts                   # 新闻相关接口的模拟数据
│   ├── university.ts             # 大学相关接口的模拟数据
│   ├── file.ts                   # 文件上传相关接口的模拟数据
│   └── weixin.ts                 # 微信相关接口的模拟数据
├── utils/                         # 工具目录
│   ├── index.ts                  # 工具导出文件
│   └── generators.ts             # 模拟数据生成工具
└── mockAdapter.ts                # 模拟适配器配置
```

## 使用方法

模拟数据已经通过 `@alova/mock` 和 `@alova/adapter-uniapp` 集成到项目中。在开发环境中，API 请求会自动使用模拟数据进行响应。

### 启用/禁用模拟

在 `mockAdapter.ts` 中，可以通过修改 `enable` 选项来启用或禁用模拟：

```typescript
const mockAdapter = createAlovaMockAdapter(allMocks, {
  // ...
  enable: true, // 设置为 false 可禁用模拟
  // ...
})
```

### 模块说明

- **monitor.ts**: 监控相关接口，包括缓存管理等
- **syb.ts**: 首选预测相关接口，包括预测线、录取预测等
- **feedback.ts**: 反馈相关接口，包括创建反馈、反馈列表等
- **member.ts**: 会员相关接口，包括登录、注册、支付、优惠券等
- **news.ts**: 新闻相关接口，包括新闻查询等
- **university.ts**: 大学相关接口，包括院校查询、专业查询、调剂分析等
- **file.ts**: 文件相关接口，包括文件上传等
- **weixin.ts**: 微信相关接口，包括小程序码、code2session等
- **common.ts**: 通用模拟处理，作为兜底处理未匹配的接口

### 添加新的模拟数据

1. 在 `modules` 目录下创建新的模块文件或在现有文件中添加
2. 使用 `defineMock` 函数定义模拟数据
3. 在 `mockAdapter.ts` 中导入并添加到 `allMocks` 数组中

示例：

```typescript
// modules/example.ts
import { defineMock } from '@alova/mock'
import { generateMockData } from '../utils/generators'

// mockAdapter.ts
import exampleMocks from './modules/example'

export default defineMock({
  '[GET]/xcx/example': ({ query }) => {
    console.log('[Mock] GET /xcx/example', query)
    return generateMockData.baseResponse({
      id: generateMockData.id(),
      name: generateMockData.name('示例'),
      created: generateMockData.datetime(),
    })
  }
})

const allMocks = [
  // ...
  exampleMocks,
  commonMocks, // 通用处理放在最后
]
```

## 模拟数据生成工具

在 `utils/generators.ts` 中提供了一系列用于生成模拟数据的工具函数，可以在各个模块中复用：

```typescript
import { generateMockData } from '../utils'

// 生成随机ID
const id = generateMockData.id()

// 生成随机名称
const name = generateMockData.name('前缀')

// 生成随机数组
const array = generateMockData.array(index => ({
  id: generateMockData.id(),
  name: generateMockData.name(`项目${index}`)
}), 10)

// 生成基础响应对象
const response = generateMockData.baseResponse(data)

// 生成列表响应对象
const listResponse = generateMockData.listResponse(items, total, more)

// 生成业务对象
const user = generateMockData.user()
const goods = generateMockData.goods(0)
const vehSaleEmp = generateMockData.vehSaleEmp(0)
```

## 注意事项

1. 模拟数据应尽量接近真实数据结构，以便于开发和测试
2. 对于需要保持一致性的数据（如ID引用），可以使用固定值而非随机生成
3. 可以使用请求参数来定制模拟响应，例如分页、筛选等
4. 模拟数据应包含各种场景，包括成功和失败的情况
