/*
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-04-17 16:21:36
 * @LastEditTime: 2025-08-17 16:09:42
 * @LastEditors: weisheng
 * @Description: Mock适配器配置 - 集成所有模块的mock数据
 * @FilePath: /tianxuan-mini/src/api/mock/mockAdapter.ts
 * 记得注释
 */
import { uniappMockResponse, uniappRequestAdapter } from '@alova/adapter-uniapp'
import { createAlovaMockAdapter } from '@alova/mock'

// 导入所有mock模块
import commonMocks from './modules/common'
import feedbackMocks from './modules/feedback'
import fileMocks from './modules/file'
import memberMocks from './modules/member'
import monitorMocks from './modules/monitor'
import newsMocks from './modules/news'
import sybMocks from './modules/syb'
import universityMocks from './modules/university'
import weixinMocks from './modules/weixin'
// 新增的mock模块
import xcxMocks from './modules/xcx'
import xcxSybMocks from './modules/xcxSyb'
import xcxUniversityMocks from './modules/xcxUniversity'
import xcxFeedbackMocks from './modules/xcxFeedback'

// 合并所有mock定义
const allMocks = [
  monitorMocks,
  sybMocks,
  feedbackMocks,
  memberMocks,
  newsMocks,
  universityMocks,
  fileMocks,
  weixinMocks,
  // 新增的mock模块
  xcxMocks,
  xcxSybMocks,
  xcxUniversityMocks,
  xcxFeedbackMocks,
  commonMocks, // 通用处理放在最后，作为兜底
]

// 创建mock适配器
const mockAdapter = createAlovaMockAdapter(allMocks, {
  // 使用uniapp请求适配器处理非mock请求
  httpAdapter: uniappRequestAdapter,

  // 使用uniapp mock响应适配器
  onMockResponse: uniappMockResponse,

  // 根据环境变量启用/禁用mock
  enable: true,

  // 添加延迟以模拟网络延迟 (200-600ms)
  delay: Math.random() * 400 + 200,

  // 在开发环境下打印mock请求日志
  mockRequestLogger: import.meta.env.MODE === 'development',
  // 路径匹配模式 - 使用完整路径匹配
  matchMode: 'pathname',
})

export default mockAdapter
