/* tslint:disable */
/* eslint-disable */
/**
 * 天枢_接口文档 - version 版本号:3.8.8
 *
 * 用于小程序接口对接; 请求头要新增appId字段, 老调剂宝传tjb; 新的传ntjb
 *
 * OpenAPI version: 3.0.3
 *
 * Contact:
 *
 * NOTE: This file is auto generated by the alova's vscode plugin.
 *
 * https://alova.js.org/devtools/vscode
 *
 * **Do not edit the file manually.**
 */
import type { Alova, AlovaMethodCreateConfig, AlovaGenerics, Method } from 'alova';
import type { $$userConfigMap, alovaInstance } from '.';
import type apiDefinitions from './apiDefinitions';

type CollapsedAlova = typeof alovaInstance;
type UserMethodConfigMap = typeof $$userConfigMap;

type Alova2MethodConfig<Responded> =
  CollapsedAlova extends Alova<
    AlovaGenerics<
      any,
      any,
      infer RequestConfig,
      infer Response,
      infer ResponseHeader,
      infer L1Cache,
      infer L2Cache,
      infer SE
    >
  >
    ? Omit<
        AlovaMethodCreateConfig<
          AlovaGenerics<Responded, any, RequestConfig, Response, ResponseHeader, L1Cache, L2Cache, SE>,
          any,
          Responded
        >,
        'params'
      >
    : never;

// Extract the return type of transform function that define in $$userConfigMap, if it not exists, use the default type.
type ExtractUserDefinedTransformed<
  DefinitionKey extends keyof typeof apiDefinitions,
  Default
> = DefinitionKey extends keyof UserMethodConfigMap
  ? UserMethodConfigMap[DefinitionKey]['transform'] extends (...args: any[]) => any
    ? Awaited<ReturnType<UserMethodConfigMap[DefinitionKey]['transform']>>
    : Default
  : Default;
type Alova2Method<
  Responded,
  DefinitionKey extends keyof typeof apiDefinitions,
  CurrentConfig extends Alova2MethodConfig<any>
> =
  CollapsedAlova extends Alova<
    AlovaGenerics<
      any,
      any,
      infer RequestConfig,
      infer Response,
      infer ResponseHeader,
      infer L1Cache,
      infer L2Cache,
      infer SE
    >
  >
    ? Method<
        AlovaGenerics<
          CurrentConfig extends undefined
            ? ExtractUserDefinedTransformed<DefinitionKey, Responded>
            : CurrentConfig['transform'] extends (...args: any[]) => any
              ? Awaited<ReturnType<CurrentConfig['transform']>>
              : ExtractUserDefinedTransformed<DefinitionKey, Responded>,
          any,
          RequestConfig,
          Response,
          ResponseHeader,
          L1Cache,
          L2Cache,
          SE
        >
      >
    : never;

export type Baseresponse_void = {
  code?: number;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type FeedBackRequest = {
  /**
   * 用户联系方式，如邮箱地址、电话号码等
   */
  contactInfo?: string;
  /**
   * 意见内容，详细描述用户遇到的问题或者提出的建议
   */
  content?: string;
  /**
   * 意见图片
   */
  images?: string[];
  /**
   * 意见类别: BUG:Bug, CORRECT:数据纠错, ADVISE:建议, COMPLAINT:吐槽, OTHER:其他
   */
  type?: 'ADVISE' | 'BUG' | 'COMPLAINT' | 'CORRECT' | 'OTHER';
};
export type FeedBackVo = {
  /**
   * 用户联系方式，如邮箱地址、电话号码等
   */
  contactInfo?: string;
  /**
   * 意见内容，详细描述用户遇到的问题或者提出的建议
   */
  content?: string;
  /**
   * 创建时间
   */
  created?: string;
  /**
   * 意见图片
   */
  images?: string[];
  /**
   * 意见类别，例如：Bug, 数据纠错, 建议, 吐槽、其他等
   */
  type?: string;
};
export type Baseresponse_list_feedbackvo = {
  code?: number;
  data?: FeedBackVo[];
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type FeedBackFilter = {
  /**
   * 当前页
   */
  page?: number;
  /**
   * 分页条数
   */
  pageSize?: number;
};
export type Baseresponse_string = {
  code?: number;
  data?: string;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type MemberBuyPreOrderResult = {
  /**
   * 购买记录的内部订单ID
   * [required]
   */
  orderId: number;
  /**
   * 小程序发起预下单的商户订单号
   * [required]
   */
  orderNumber: string;
  /**
   * 微信返回的用于唤起小程序支付的参数
   * [required]
   */
  payInfo: string;
};
export type Baseresponse_memberbuypreorderresult = {
  code?: number;
  /**
   * MemberBuyPreOrderResult
   * ---
   */
  data?: MemberBuyPreOrderResult;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type Kfbhddgdhc = {
  /**
   * 福利码
   */
  couponCode?: string;
  /**
   * OPENID
   * [required]
   */
  openid: string;
};
export type MemberBuyPayResult = {
  /**
   * 购买记录的内部订单ID
   * [required]
   */
  orderId: number;
  /**
   * 小程序发起预下单的商户订单号
   * [required]
   */
  orderNumber: string;
  /**
   * 支付结果: 0 成功 1失败 2支付中
   * [required]
   */
  payState: number;
};
export type Baseresponse_memberbuypayresult = {
  code?: number;
  /**
   * MemberBuyPayResult
   * ---
   */
  data?: MemberBuyPayResult;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type Kbgbgeebef = {
  /**
   * 订单ID
   * [required]
   */
  orderId: number;
  /**
   * 支付商户订单号,前端生成
   * [required]
   */
  orderNumber: string;
};
export type MemberBuyPrice = {
  /**
   * 福利码优惠后价格
   * [required]
   */
  couponPrice: number;
  /**
   * 标准价格
   * [required]
   */
  rtlPrice: number;
  /**
   * 虚拟原价
   * [required]
   */
  virtualPrice: number;
};
export type Baseresponse_memberbuyprice = {
  code?: number;
  /**
   * MemberBuyPrice
   * ---
   */
  data?: MemberBuyPrice;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type Baseresponse_boolean = {
  code?: number;
  data?: boolean;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type Kfaigihfae = {
  /**
   * 微信用户openId
   */
  openid?: string;
  /**
   * 会话密钥
   */
  sessionKey?: string;
  /**
   * 微信用户unionid
   */
  unionid?: string;
};
export type Baseresponse = {
  code?: number;
  /**
   * 微信用户信息
   * ---
   */
  data?: Kfaigihfae;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type Code2SessionRequest = {
  /**
   * 凭据
   */
  code?: string;
};
export type Bgbfhjgacd = {
  /**
   * 福利码
   * [required]
   */
  couponCode: string;
};
export type MemberCouponResult = {
  /**
   * 福利码
   * [required]
   */
  couponCode: string;
  /**
   * 领取时间
   * [required]
   */
  created: string;
  /**
   * 会员ID
   * [required]
   */
  memberId: string;
};
export type Baseresponse_list_membercouponresult = {
  code?: number;
  data?: MemberCouponResult[];
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type Bebgdbjddb = object;
export type DecryptRequest = {
  data?: string;
  iv?: string;
  key?: string;
};
export type Baseresponse_int = {
  code?: number;
  data?: number;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type Gejdec = {
  /**
   * 头像
   */
  avatar?: string;
  /**
   * 业务课一代码
   */
  biz1Code?: string;
  /**
   * 业务课一名称
   */
  biz1Name?: string;
  /**
   * 业务课一成绩
   */
  biz1Score?: number;
  /**
   * 业务课二代码
   */
  biz2Code?: string;
  /**
   * 业务课二名称
   */
  biz2Name?: string;
  /**
   * 业务课二成绩
   */
  biz2Score?: number;
  /**
   * 院系所代码
   */
  departmentCode?: string;
  /**
   * 院系所名称
   */
  departmentName?: string;
  /**
   * 研究方向代码
   */
  directionCode?: string;
  /**
   * 研究方向名称
   */
  directionName?: string;
  /**
   * 准考证号
   */
  examRegistrationNumber?: string;
  /**
   * 一志愿专业代码
   */
  firstChoiceDegreeCode?: string;
  /**
   * 一志愿专业名称
   */
  firstChoiceDegreeName?: string;
  /**
   * 一志愿学校代码
   */
  firstChoiceSchoolCode?: string;
  /**
   * 一志愿学校名称
   */
  firstChoiceSchoolName?: string;
  /**
   * 初试成绩
   */
  firstScore?: number;
  /**
   * 外语课代码
   */
  foreignLanguageCode?: string;
  /**
   * 外语课名称
   */
  foreignLanguageName?: string;
  /**
   * 外语成绩
   */
  foreignLanguageScore?: number;
  /**
   * 会员ID
   */
  id?: string;
  /**
   * 会员手机号
   */
  mobile?: string;
  /**
   * 会员名称
   */
  name?: string;
  /**
   * 会员昵称
   */
  nickName?: string;
  /**
   * 会员微信OPENID
   */
  openid?: string;
  /**
   * 政治课代码
   */
  politicsCode?: string;
  /**
   * 政治课名称
   */
  politicsName?: string;
  /**
   * 政治成绩
   */
  politicsScore?: number;
  /**
   * 全日志1/非全日制0
   */
  studyType?: string;
  /**
   * 普通用户0/付费用户1
   */
  type?: number;
  /**
   * 会员微信unionid
   */
  unionid?: string;
};
export type Baseresponse1 = {
  code?: number;
  /**
   * 会员
   * ---
   */
  data?: Gejdec;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type Kgigdhbcdb = {
  /**
   * 会员
   * ---
   */
  member?: Gejdec;
};
export type Baseresponse2 = {
  code?: number;
  /**
   * 会员登录结果
   * ---
   */
  data?: Kgigdhbcdb;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type Kbjgjejfbia = {
  /**
   * 会员登录的微信小程序openid
   */
  openid?: string;
};
export type Bbfgjbbbif = {
  /**
   * 昵称
   */
  nickName?: string;
};
export type Kjceaejjbh = {
  /**
   * 业务课一代码
   */
  biz1Code?: string;
  /**
   * 业务课一名称
   */
  biz1Name?: string;
  /**
   * 业务课一成绩
   */
  biz1Score?: number;
  /**
   * 业务课二代码
   */
  biz2Code?: string;
  /**
   * 业务课二名称
   */
  biz2Name?: string;
  /**
   * 业务课二成绩
   */
  biz2Score?: number;
  /**
   * 院系所代码
   */
  departmentCode?: string;
  /**
   * 院系所名称
   */
  departmentName?: string;
  /**
   * 研究方向代码
   */
  directionCode?: string;
  /**
   * 研究方向名称
   */
  directionName?: string;
  /**
   * 准考证号
   */
  examRegistrationNumber?: string;
  /**
   * 一志愿专业代码
   */
  firstChoiceDegreeCode?: string;
  /**
   * 一志愿专业名称
   */
  firstChoiceDegreeName?: string;
  /**
   * 一志愿学校代码
   */
  firstChoiceSchoolCode?: string;
  /**
   * 一志愿学校名称
   */
  firstChoiceSchoolName?: string;
  /**
   * 初试成绩
   */
  firstScore?: number;
  /**
   * 外语课代码
   */
  foreignLanguageCode?: string;
  /**
   * 外语课名称
   */
  foreignLanguageName?: string;
  /**
   * 外语成绩
   */
  foreignLanguageScore?: number;
  /**
   * 会员ID
   */
  memberId?: string;
  /**
   * 政治课代码
   */
  politicsCode?: string;
  /**
   * 政治课名称
   */
  politicsName?: string;
  /**
   * 政治成绩
   */
  politicsScore?: number;
  /**
   * 全日制1/非全日制0
   */
  studyType?: string;
};
export type Kcaefjaecja = {
  /**
   * 会员手机号
   * [required]
   */
  mobile: string;
  /**
   * 会员昵称
   * [required]
   */
  nickName: string;
  /**
   * 会员微信OPENID
   * [required]
   */
  openid: string;
  /**
   * 会员微信unionid
   * [required]
   */
  unionid: string;
};
export type MasterSlavePic = {
  /**
   * 子图
   */
  childrenPics?: string[];
  /**
   * 主图
   */
  masterPic?: string;
};
export type MemberRightsVo = {
  /**
   * 轮播图
   */
  bannerPics?: MasterSlavePic[];
  /**
   * 客服二维码
   */
  customerServiceQrcode?: string;
  /**
   * MasterSlavePic
   * ---
   */
  popupPic?: MasterSlavePic;
  /**
   * 会员权益说明图片
   */
  rightsPic?: string;
  /**
   * 用户手册
   */
  userGuide?: string;
};
export type Baseresponse_memberrightsvo = {
  code?: number;
  /**
   * MemberRightsVo
   * ---
   */
  data?: MemberRightsVo;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type News = {
  /**
   * 公告内容
   */
  content?: string;
  /**
   * 创建时间
   */
  created?: string;
  degreeType?: string;
  /**
   * 院系所代码
   */
  departmentCode?: string;
  /**
   * 院系所名称
   */
  departmentName?: string;
  learnType?: string;
  /**
   * 公告外部链接
   */
  linkUrl?: string;
  /**
   * 专业代码
   */
  majorCode?: string;
  /**
   * 专业名称
   */
  majorName?: string;
  /**
   * 来源渠道
   */
  source?: string;
  /**
   * 公告标题
   */
  title?: string;
  /**
   * 招生单位代码
   */
  unitCode?: string;
  /**
   * 招生单位名称
   */
  unitName?: string;
};
export type Baseresponse_list_news = {
  code?: number;
  data?: News[];
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type NewsFilter = {
  /**
   * 学校/专业/代码搜索
   */
  keyword?: string;
  /**
   * 学习方式等于
   */
  learnTypeEquals?: string;
  /**
   * 专业代码等于
   */
  majorCodeEquals?: string;
  /**
   * 专业代码在。。。之间
   */
  majorCodeIn?: string[];
  /**
   * 会员ID
   */
  memberId?: string;
  page?: number;
  pageSize?: number;
  /**
   * 院校属性在。。。之间
   */
  propIn?: string[];
  /**
   * 省份代码等于
   */
  provinceCodeEquals?: string;
  /**
   * 省份代码在。。。之间
   */
  provinceCodeIn?: string[];
  /**
   * 院校代码等于
   */
  uniCodeEquals?: string;
};
export type AdjustAnalysisResultDetailVo = {
  /**
   * 学位类型
   */
  degreeType?: string;
  /**
   * 学习方式
   */
  learnType?: string;
  /**
   * 专业代码
   */
  majorCode?: string;
  /**
   * 专业名称
   */
  majorName?: string;
  /**
   * 一级学科代码
   */
  oneSubjectCode?: string;
  /**
   * 一级学科名称
   */
  oneSubjectName?: string;
  /**
   * 院校属性
   */
  props?: string[];
  /**
   * 院校所属区（A、B）
   */
  provinceType?: string;
  /**
   * 门类代码
   */
  subjectCategoryCode?: string;
  /**
   * 门类名称
   */
  subjectCategoryName?: string;
  /**
   * 院校代码
   */
  uniCode?: string;
  /**
   * 院校名称
   */
  uniName?: string;
};
export type AdjustAnalysisResultVo = {
  /**
   * 可参加调剂的院校数量
   */
  adjustUniTotal?: number;
  /**
   * 考生类别
   */
  candidateType?: string;
  /**
   * 不区分A、B区的所有调剂院校专业信息
   */
  details?: AdjustAnalysisResultDetailVo[];
  /**
   * A区可调剂院校专业信息
   */
  detailsA?: AdjustAnalysisResultDetailVo[];
  /**
   * B区可调剂院校专业信息
   */
  detailsB?: AdjustAnalysisResultDetailVo[];
  /**
   * 是否大于等于A区国家线
   */
  gteA?: boolean;
  /**
   * 同类型的院校数量
   */
  sameTypeUniTotal?: number;
};
export type Baseresponse_adjustanalysisresultvo = {
  code?: number;
  /**
   * AdjustAnalysisResultVo
   * ---
   */
  data?: AdjustAnalysisResultVo;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type SameScoreDest4AdjustAnalysisChartItemDetailVo = {
  /**
   * 院校属性对应数量
   */
  count?: number;
  /**
   * 院校属性
   */
  prop?: string;
};
export type SameScoreDest4AdjustAnalysisChartItemVo = {
  /**
   * 院校属性及其对应的院校数量
   */
  details?: SameScoreDest4AdjustAnalysisChartItemDetailVo[];
  /**
   * 年份
   */
  year?: string;
};
export type SameScoreDest4AdjustAnalysisChartVo = {
  items?: SameScoreDest4AdjustAnalysisChartItemVo[];
};
export type Baseresponse_samescoredest4adjustanalysischartvo = {
  code?: number;
  /**
   * SameScoreDest4AdjustAnalysisChartVo
   * ---
   */
  data?: SameScoreDest4AdjustAnalysisChartVo;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type SameScoreDest4AdjustAnalysisFilter = object;
export type SameUniDest4AdjustAnalysisChartItemDetailVo = {
  /**
   * 院校属性对应数量
   */
  count?: number;
  /**
   * 院校属性
   */
  prop?: string;
};
export type SameUniDest4AdjustAnalysisChartItemVo = {
  /**
   * 院校属性及其对应的院校数量
   */
  details?: SameUniDest4AdjustAnalysisChartItemDetailVo[];
  /**
   * 年份
   */
  year?: string;
};
export type SameUniDest4AdjustAnalysisChartVo = {
  items?: SameUniDest4AdjustAnalysisChartItemVo[];
};
export type Baseresponse_sameunidest4adjustanalysischartvo = {
  code?: number;
  /**
   * SameUniDest4AdjustAnalysisChartVo
   * ---
   */
  data?: SameUniDest4AdjustAnalysisChartVo;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type SameUniDest4AdjustAnalysisFilter = object;
export type ExamVo = {
  /**
   * 业务课一代码
   */
  bizCourseOneCode?: string;
  /**
   * 业务课一名称
   */
  bizCourseOneName?: string;
  /**
   * 业务课二代码
   */
  bizCourseTwoCode?: string;
  /**
   * 业务课二名称
   */
  bizCourseTwoName?: string;
  /**
   * 外语课代码
   */
  foreignLanguageCode?: string;
  /**
   * 外语课名称
   */
  foreignLanguageName?: string;
  /**
   * 政治课代码
   */
  politicsCode?: string;
  /**
   * 政治课名称
   */
  politicsName?: string;
};
export type ReferBookVo = {
  /**
   * 作者
   */
  author?: string;
  /**
   * 书名
   */
  name?: string;
  /**
   * 出版社
   */
  publishHouse?: string;
  /**
   * 出版年份
   */
  year?: string;
};
export type SecondCourseVo = {
  /**
   * 复试科目代码
   */
  code?: string;
  /**
   * 复试科目名称
   */
  name?: string;
};
export type AdmissionItem = {
  /**
   * 学位类型
   */
  degreeType?: string;
  /**
   * 研究方向代码
   */
  directionCode?: string;
  /**
   * 研究方向名称
   */
  directionName?: string;
  /**
   * 考试科目（四门业务课）
   */
  examList?: ExamVo[];
  /**
   * 指导教师
   */
  guideTeacher?: string;
  /**
   * 学习方式
   */
  learnType?: string;
  /**
   * 是否少数民族骨干计划
   */
  minorityCorePlan?: boolean;
  /**
   * 拟招生人数
   */
  proposedAdmissionNum?: number;
  /**
   * ReferBookVo
   * ---
   */
  referBook?: ReferBookVo;
  /**
   * 是否退役计划
   */
  retirePlan?: boolean;
  /**
   * 复试科目
   */
  secondCourseList?: SecondCourseVo[];
};
export type AdmissionBaseVo = {
  /**
   * 招生明细
   */
  admissionItems?: AdmissionItem[];
};
export type Baseresponse_admissionbasevo = {
  code?: number;
  /**
   * AdmissionBaseVo
   * ---
   */
  data?: AdmissionBaseVo;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type AdmissionBaseFilter = {
  /**
   * 学位类型等于
   */
  degreeTypeEquals?: string;
  /**
   * 研究方向代码
   */
  directionCodeEquals?: string;
  /**
   * 学习方式等于
   * [required]
   */
  learnTypeEquals: string;
  /**
   * 专业代码等于
   * [required]
   */
  majorCodeEquals: string;
  /**
   * 招生单位代码等于
   * [required]
   */
  unitCodeEquals: string;
};
export type AdmissionLast3YearsVo = {
  /**
   * 调剂录取人数
   */
  adjust?: number;
  /**
   * 一志愿录取人数
   */
  oneVolunteer?: number;
  /**
   * 录取总数
   */
  total?: number;
  /**
   * 年份
   */
  year?: string;
};
export type Baseresponse_list_admissionlast3yearsvo = {
  code?: number;
  data?: AdmissionLast3YearsVo[];
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type AdmissionLast3YearsFilter = {
  /**
   * 学位类型等于
   */
  degreeTypeEquals?: string;
  /**
   * 学习方式等于
   */
  learnTypeEquals?: string;
  /**
   * 专业代码等于
   */
  majorCodeEquals?: string;
  /**
   * 招生单位代码等于
   */
  uniCodeEquals?: string;
};
export type AdmissionLast3YearsScoreRangeVo = {
  /**
   * 录取调整最高分
   */
  adjustMax?: number;
  /**
   * 录取调整最低分
   */
  adjustMin?: number;
  /**
   * 一志愿录取最低分
   */
  firstChoiceMax?: number;
  /**
   * 一志愿录取最低分
   */
  firstChoiceMin?: number;
  /**
   * 年份
   */
  year?: string;
};
export type Baseresponse_list_admissionlast3yearsscorerangevo = {
  code?: number;
  data?: AdmissionLast3YearsScoreRangeVo[];
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type AdmissionLast3YearsScoreRangeFilter = {
  /**
   * 学位类型等于
   */
  degreeTypeEquals?: string;
  /**
   * 学习方式等于
   */
  learnTypeEquals?: string;
  /**
   * 专业代码等于
   */
  majorCodeEquals?: string;
  /**
   * 招生单位代码等于
   */
  uniCodeEquals?: string;
};
export type AdmissionProposedNameVo = {
  /**
   * 录取方式
   */
  admissionType?: string;
  /**
   * 业务课一成绩
   */
  bizOne?: number;
  /**
   * 业务课二成绩
   */
  bizTwo?: number;
  /**
   * 初试成绩
   */
  first?: number;
  /**
   * 初试成绩排名
   */
  firstRank?: number;
  /**
   * 外语成绩
   */
  foreign?: number;
  /**
   * 姓名
   */
  name?: string;
  /**
   * 一志愿院校代码
   */
  oneUniversityCode?: string;
  /**
   * 一志愿院校名称
   */
  oneUniversityName?: string;
  /**
   * 政治成绩
   */
  politics?: number;
  /**
   * 复试成绩
   */
  second?: number;
  /**
   * 总成绩
   */
  total?: number;
  /**
   * 年份
   */
  year?: string;
};
export type AdmissionProposedName4AnalysisVo = {
  /**
   * 拟录取名单数据
   */
  admPnList?: AdmissionProposedNameVo[];
  /**
   * 专业是否新增专业
   */
  majorIsNew?: boolean;
};
export type Baseresponse_admissionproposedname4analysisvo = {
  code?: number;
  /**
   * AdmissionProposedName4AnalysisVo
   * ---
   */
  data?: AdmissionProposedName4AnalysisVo;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type AdmissionProposedNameFilter = {
  /**
   * 学位类型等于
   */
  degreeTypeEquals?: string;
  /**
   * 学习方式等于
   */
  learnTypeEquals?: string;
  /**
   * 专业代码等于
   */
  majorCodeEquals?: string;
  /**
   * 当前页
   */
  page?: number;
  /**
   * 分页条数
   */
  pageSize?: number;
  /**
   * 招生单位代码等于
   */
  uniCodeEquals?: string;
  /**
   * 年份在。。。之间
   * [required]
   */
  yearsIn: string[];
};
export type AdmissionMajorCatalogVo = {
  /**
   * 业务课一代码
   */
  bizCourseOneCode?: string;
  /**
   * 业务课一名称
   */
  bizCourseOneName?: string;
  /**
   * 业务课二代码
   */
  bizCourseTwoCode?: string;
  /**
   * 业务课二名称
   */
  bizCourseTwoName?: string;
  /**
   * 学位类型
   */
  degreeType?: string;
  /**
   * 院系所代码
   */
  departmentCode?: string;
  /**
   * 院系所名称
   */
  departmentName?: string;
  /**
   * 研究方向代码
   */
  directionCode?: string;
  /**
   * 研究方向名称
   */
  directionName?: string;
  /**
   * 考试方式
   */
  examType?: string;
  /**
   * 外语课代码
   */
  foreignLanguageCode?: string;
  /**
   * 外语课名称
   */
  foreignLanguageName?: string;
  /**
   * 学习方式
   */
  learnType?: string;
  /**
   * 专业代码
   */
  majorCode?: string;
  /**
   * 专业名称
   */
  majorName?: string;
  /**
   * 政治课代码
   */
  politicsCode?: string;
  /**
   * 政治课名称
   */
  politicsName?: string;
  /**
   * 拟招生人数
   */
  proposedAdmissionNum?: number;
  /**
   * 是否发布调剂公告
   */
  publishedAdjustNotice?: boolean;
  /**
   * 招生单位代码
   */
  unitCode?: string;
  /**
   * 招生单位名称
   */
  unitName?: string;
};
export type Baseresponse_list_admissionmajorcatalogvo = {
  code?: number;
  data?: AdmissionMajorCatalogVo[];
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type AdmissionMajorCatalogFilter = {
  /**
   * 院系所代码等于
   */
  departmentCodeEquals?: string;
  /**
   * 研究方向代码等于
   */
  directionCodeEquals?: string;
  /**
   * 招生专业目录主键等于
   */
  idEquals?: string;
  /**
   * 学习方式等于
   */
  learnTypeEquals?: string;
  /**
   * 专业代码等于
   */
  majorCodeEquals?: string;
  /**
   * 当前页
   */
  page?: number;
  /**
   * 分页条数
   */
  pageSize?: number;
  /**
   * 院校属性在。。。之间
   */
  propIn?: string[];
  /**
   * 省份代码等于
   */
  provinceCodeEquals?: string;
  /**
   * 省份代码在。。。之间
   */
  provinceCodeIn?: string[];
  /**
   * 招生单位代码等于
   */
  uniCodeEquals?: string;
  /**
   * 当年
   */
  year?: string;
};
export type CodeNameVo = {
  /**
   * 代码
   */
  code?: string;
  /**
   * 名称
   */
  name?: string;
};
export type Baseresponse_list_codenamevo = {
  code?: number;
  data?: CodeNameVo[];
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type CodeNameFilter = {
  /**
   * 配置类型
   */
  type?: string;
};
export type AdmissionAdjustMaxScoreItemVo = {
  adjustMax?: number;
  year?: string;
};
export type AdmissionAdjustMaxScoreVo = {
  items?: AdmissionAdjustMaxScoreItemVo[];
};
export type AdmissionAdjustMinScoreItemVo = {
  adjustMin?: number;
  year?: string;
};
export type AdmissionAdjustMinScoreVo = {
  items?: AdmissionAdjustMinScoreItemVo[];
};
export type AdjustNumItemVo = {
  adjustNum?: number;
  year?: string;
};
export type AdjustNumVo = {
  items?: AdjustNumItemVo[];
};
export type AdjustSource4CompareVo = {
  oneVolunteerUniversityCode?: string;
  oneVolunteerUniversityName?: string;
  total?: number;
};
export type AdmissionNumInfoVo = {
  /**
   * 招生人数
   */
  admissionNum?: number;
  /**
   * 招生类型
   */
  type?: string;
};
export type AdmissionFirstChoiceMaxScoreItemVo = {
  firstChoiceMax?: number;
  year?: string;
};
export type AdmissionFirstChoiceMaxScoreVo = {
  items?: AdmissionFirstChoiceMaxScoreItemVo[];
};
export type AdmissionFirstChoiceMinScoreItemVo = {
  firstChoiceMin?: number;
  year?: string;
};
export type AdmissionFirstChoiceMinScoreVo = {
  items?: AdmissionFirstChoiceMinScoreItemVo[];
};
export type CompareResultItemVo = {
  /**
   * AdmissionAdjustMaxScoreVo
   * ---
   */
  adjustMax?: AdmissionAdjustMaxScoreVo;
  /**
   * AdmissionAdjustMinScoreVo
   * ---
   */
  adjustMin?: AdmissionAdjustMinScoreVo;
  /**
   * AdjustNumVo
   * ---
   */
  adjustNum?: AdjustNumVo;
  /**
   * 调剂来源最多的院校(近四年)
   */
  adjustSourceUniList?: AdjustSource4CompareVo[];
  /**
   * 招生人数
   */
  admissionNumInfoList?: AdmissionNumInfoVo[];
  /**
   * 学位类型等于
   */
  degreeType?: string;
  /**
   * AdmissionFirstChoiceMaxScoreVo
   * ---
   */
  firstChoiceMax?: AdmissionFirstChoiceMaxScoreVo;
  /**
   * AdmissionFirstChoiceMinScoreVo
   * ---
   */
  firstChoiceMin?: AdmissionFirstChoiceMinScoreVo;
  /**
   * 初试科目
   */
  firstExamList?: ExamVo[];
  /**
   * 学习方式
   */
  learnType?: string;
  /**
   * 专业代码
   */
  majorCode?: string;
  /**
   * 专业名称
   */
  majorName?: string;
  /**
   * 学校排名
   */
  rank?: string;
  /**
   * 推荐类型
   */
  recommendType?: string;
  /**
   * 院校代码
   */
  uniCode?: string;
  /**
   * 院校名称
   */
  uniName?: string;
};
export type CompareResultVo = {
  /**
   * CompareResultItemVo
   * ---
   */
  source?: CompareResultItemVo;
  /**
   * CompareResultItemVo
   * ---
   */
  target?: CompareResultItemVo;
};
export type Baseresponse_compareresultvo = {
  code?: number;
  /**
   * CompareResultVo
   * ---
   */
  data?: CompareResultVo;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type CompareItemFilter = {
  /**
   * 学位类型等于
   * [required]
   */
  degreeTypeEquals: string;
  /**
   * 学习方式等于
   * [required]
   */
  learnTypeEquals: string;
  /**
   * 专业代码等于
   * [required]
   */
  majorCodeEquals: string;
  /**
   * 院校代码等于
   * [required]
   */
  uniCodeEquals: string;
};
export type CompareFilter = {
  /**
   * CompareItemFilter
   * ---
   * [required]
   */
  source: CompareItemFilter;
  /**
   * CompareItemFilter
   * ---
   * [required]
   */
  target: CompareItemFilter;
};
export type DepartmentQueryForRegisterVo = {
  /**
   * 院系所代码
   */
  code?: string;
  /**
   * 院系所名称
   */
  name?: string;
};
export type Baseresponse_list_departmentqueryforregistervo = {
  code?: number;
  data?: DepartmentQueryForRegisterVo[];
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type DepartmentQueryForRegisterFilter = {
  /**
   * 院系所代码等于或名称类似于
   */
  codeEqualsOrNameLikes?: string;
  /**
   * 学习方式等于
   */
  learnTypeEquals?: string;
  /**
   * 当前页
   */
  page?: number;
  /**
   * 分页条数
   */
  pageSize?: number;
  /**
   * 院校代码等于
   */
  uniCodeEquals?: string;
};
export type DirectionQueryForRegisterVo = {
  /**
   * 研究方向代码
   */
  code?: string;
  /**
   * 研究方向名称
   */
  name?: string;
};
export type Baseresponse_list_directionqueryforregistervo = {
  code?: number;
  data?: DirectionQueryForRegisterVo[];
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type DirectionQueryForRegisterFilter = {
  /**
   * 研究方向代码等于或名称类似于
   */
  codeEqualsOrNameLikes?: string;
  /**
   * 院系所代码等于
   */
  departmentCodeEquals?: string;
  /**
   * 学习方式等于
   */
  learnTypeEquals?: string;
  /**
   * 专业代码等于
   */
  majorCodeEquals?: string;
  /**
   * 当前页
   */
  page?: number;
  /**
   * 分页条数
   */
  pageSize?: number;
  /**
   * 院校代码等于
   */
  uniCodeEquals?: string;
};
export type MyCollectRequest = {
  /**
   * 学位类型: 1：学术学位、2：专业学位
   */
  degreeType?: string;
  /**
   * 学习方式: 1：全日制、0：非全日制
   */
  learnType?: string;
  /**
   * 专业代码
   */
  majorCode?: string;
  /**
   * 学院代码
   */
  uniCode?: string;
};
export type MyCollectVo = {
  /**
   * 学位类型
   */
  degreeType?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 学习方式
   */
  learnType?: string;
  /**
   * 专业代码
   */
  majorCode?: string;
  /**
   * 专业名称
   */
  majorName?: string;
  /**
   * 电话订阅
   */
  phoneSub?: boolean;
  /**
   * 院校属性
   */
  props?: string[];
  /**
   * 短信订阅
   */
  smsSub?: boolean;
  /**
   * 院校代码
   */
  uniCode?: string;
  /**
   * 院校名称
   */
  uniName?: string;
  /**
   * 微信消息订阅
   */
  wxSub?: boolean;
};
export type Baseresponse_list_mycollectvo = {
  code?: number;
  data?: MyCollectVo[];
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type MyCollectFilter = {
  /**
   * 当前页
   */
  page?: number;
  /**
   * 分页条数
   */
  pageSize?: number;
};
export type FavSubscribeRequest = {
  /**
   * 收藏表主键
   */
  id?: number;
  /**
   * 收藏类型
   */
  subType?: 'PHONE' | 'SMS' | 'WX';
};
export type FavUnSubscribeRequest = {
  /**
   * 收藏表主键
   */
  id?: number;
  /**
   * 收藏类型
   */
  subType?: 'PHONE' | 'SMS' | 'WX';
};
export type MyRankVo = {
  /**
   * 我的排名
   */
  myRank?: number;
  /**
   * 总排名
   */
  total?: number;
};
export type Baseresponse_myrankvo = {
  code?: number;
  /**
   * MyRankVo
   * ---
   */
  data?: MyRankVo;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type MyRankFilter = {
  /**
   * 学习方式等于
   */
  learnTypeEquals?: string;
  /**
   * 专业代码等于
   */
  majorCodeEquals?: string;
  /**
   * 院校属性在。。。之间
   */
  propIn?: string[];
  /**
   * 省份代码在。。。之间
   */
  provinceCodeIn?: string[];
  /**
   * 年份
   */
  yearEquals?: string;
};
export type UniItemVo = {
  /**
   * 学位类型
   */
  degreeType?: string;
  /**
   * 院系所代码
   */
  departmentCode?: string;
  /**
   * 院系所名称
   */
  departmentName?: string;
  /**
   * 研究方向代码
   */
  directionCode?: string;
  /**
   * 研究方向名称
   */
  directionName?: string;
  /**
   * 学习方式
   */
  learnType?: string;
  /**
   * 专业代码
   */
  majorCode?: string;
  /**
   * 专业名称
   */
  majorName?: string;
  /**
   * 一级学科代码
   */
  oneSubjectCode?: string;
  /**
   * 一级学科名称
   */
  oneSubjectName?: string;
  /**
   * 院校属性
   */
  props?: string[];
  /**
   * 门类代码
   */
  subjectCategoryCode?: string;
  /**
   * 门类名称
   */
  subjectCategoryName?: string;
  /**
   * 院校录取人数
   */
  total?: number;
  /**
   * 院校代码
   */
  uniCode?: string;
  /**
   * 院校名称
   */
  uniName?: string;
};
export type OneScoreOneRangeVo = {
  /**
   * 初试成绩(区间或只有一个成绩)
   */
  first?: number[];
  /**
   * 排名
   */
  rank?: number;
  /**
   * 分数是否命中此区间
   */
  scoreHit?: boolean;
  /**
   * 人数
   */
  total?: number;
  uniItems?: UniItemVo[];
  /**
   * 年份
   */
  year?: string;
};
export type Baseresponse_list_onescoreonerangevo = {
  code?: number;
  data?: OneScoreOneRangeVo[];
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type OneScoreOneRangeFilter = {
  /**
   * 学习方式等于
   */
  learnTypeEquals?: string;
  /**
   * 专业代码等于
   */
  majorCodeEquals?: string;
  /**
   * 院校属性在。。。之间
   */
  propIn?: string[];
  /**
   * 省份代码在。。。之间
   */
  provinceCodeIn?: string[];
  /**
   * 年份
   */
  yearEquals?: string;
};
export type SameScoreDestVo = {
  /**
   * 录取院校代码
   */
  admissionUniCode?: string;
  /**
   * 录取院校名称
   */
  admissionUniName?: string;
  /**
   * 初试成绩
   */
  first?: number;
  /**
   * 学习方式
   */
  learnType?: string;
  /**
   * 专业代码
   */
  majorCode?: string;
  /**
   * 专业名称
   */
  majorName?: string;
  /**
   * 姓名
   */
  name?: string;
  /**
   * 年份
   */
  year?: string;
};
export type Baseresponse_list_samescoredestvo = {
  code?: number;
  data?: SameScoreDestVo[];
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type SameScoreDestFilter = {
  /**
   * 初试成绩浮动范围值
   */
  firstScoreFloat?: number;
  /**
   * 学习方式等于
   */
  learnTypeEquals?: string;
  /**
   * 专业代码等于
   */
  majorCodeEquals?: string;
  /**
   * 当前页
   */
  page?: number;
  /**
   * 分页条数
   */
  pageSize?: number;
  /**
   * 院校属性在。。。之间
   */
  propIn?: string[];
  /**
   * 省份代码在。。。之间
   */
  provinceCodeIn?: string[];
  /**
   * 年份
   */
  yearEquals?: string;
};
export type SameScoreDestStatisticsProvinceInfoVo = {
  /**
   * 同分去向的省份代码
   */
  provinceCode?: string;
  /**
   * 同分去向的省份名称
   */
  provinceName?: string;
  /**
   * 同分去向的院校省份对应人数所占比例
   */
  rate?: number;
  /**
   * 同分去向的院校省份对应人数
   */
  total?: number;
};
export type SameScoreDestStatisticsUniInfoVo = {
  /**
   * 同分去向的院校对应人数所占比例
   */
  rate?: number;
  /**
   * 同分去向的院校对应人数
   */
  total?: number;
  /**
   * 同分去向的院校代码
   */
  uniCode?: string;
  /**
   * 同分去向的院校名称
   */
  uniName?: string;
};
export type SameScoreDestStatisticsVo = {
  /**
   * 最多去向省份
   */
  mostProvinceList?: SameScoreDestStatisticsProvinceInfoVo[];
  /**
   * 最多去向院校
   */
  mostUniList?: SameScoreDestStatisticsUniInfoVo[];
};
export type Baseresponse_samescoredeststatisticsvo = {
  code?: number;
  /**
   * SameScoreDestStatisticsVo
   * ---
   */
  data?: SameScoreDestStatisticsVo;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type SameUniDestVo = {
  /**
   * 录取院校代码
   */
  admissionUniCode?: string;
  /**
   * 录取院校名称
   */
  admissionUniName?: string;
  /**
   * 初试成绩
   */
  first?: number;
  /**
   * 学习方式
   */
  learnType?: string;
  /**
   * 专业代码
   */
  majorCode?: string;
  /**
   * 专业名称
   */
  majorName?: string;
  /**
   * 姓名
   */
  name?: string;
  /**
   * 年份
   */
  year?: string;
};
export type Baseresponse_list_sameunidestvo = {
  code?: number;
  data?: SameUniDestVo[];
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type SameUniDestFilter = {
  /**
   * 初试成绩浮动范围值
   */
  firstScoreFloat?: number;
  /**
   * 学习方式等于
   */
  learnTypeEquals?: string;
  /**
   * 专业代码等于
   */
  majorCodeEquals?: string;
  /**
   * 当前页
   */
  page?: number;
  /**
   * 分页条数
   */
  pageSize?: number;
  /**
   * 省份代码在。。。之间
   */
  provinceCodeIn?: string[];
  /**
   * 院校代码等于
   */
  uniCodeEquals?: string;
  /**
   * 年份
   */
  yearEquals?: string;
};
export type SameUniDestStatisticsProvinceInfoVo = {
  /**
   * 同校去向的院校省份代码
   */
  provinceCode?: string;
  /**
   * 同校去向的院校省份名称
   */
  provinceName?: string;
  /**
   * 同校去向的院校省份对应人数所占比例
   */
  rate?: number;
  /**
   * 同校去向的院校省份对应人数
   */
  total?: number;
};
export type SameUniDestStatisticsUniInfoVo = {
  /**
   * 同校去向的院校对应人数所占比例
   */
  rate?: number;
  /**
   * 同校去向的院校对应人数
   */
  total?: number;
  /**
   * 同校去向的院校代码
   */
  uniCode?: string;
  /**
   * 同校去向的院校名称
   */
  uniName?: string;
};
export type SameUniDestStatisticsVo = {
  /**
   * 最多去向省份
   */
  mostProvinceList?: SameUniDestStatisticsProvinceInfoVo[];
  /**
   * 最多去向院校
   */
  mostUniList?: SameUniDestStatisticsUniInfoVo[];
};
export type Baseresponse_sameunideststatisticsvo = {
  code?: number;
  /**
   * SameUniDestStatisticsVo
   * ---
   */
  data?: SameUniDestStatisticsVo;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type FirstChoiceYearAnalysisVo = {
  /**
   * 分数和最高分比较结果描述
   */
  compareMaxDesc?: string;
  /**
   * 分数和最低分比较结果描述
   */
  compareMinDesc?: string;
  /**
   * 一志愿录取最高分
   */
  firstChoiceAdmMax?: number;
  /**
   * 一志愿录取成绩中位数
   */
  firstChoiceAdmMedian?: number;
  /**
   * 一志愿录取最低分
   */
  firstChoiceAdmMin?: number;
  /**
   * 一志愿拟录取总人数
   */
  firstChoiceProposedAdmNum?: number;
  firstRank?: number;
};
export type Baseresponse_firstchoiceyearanalysisvo = {
  code?: number;
  /**
   * FirstChoiceYearAnalysisVo
   * ---
   */
  data?: FirstChoiceYearAnalysisVo;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type FirstChoiceYearAnalysisFilter = {
  /**
   * 是否含专项计划
   */
  hasSpecialPlan?: boolean;
  /**
   * 年份
   */
  yearEquals?: string;
};
export type FirstChoicePredictNationLineVo = {
  /**
   * 单科满分等于100的分数线
   */
  single100?: number;
  /**
   * 单科满分超过100的分数线
   */
  singleUp100?: number;
  /**
   * 总分
   */
  total?: number;
  /**
   * 年份
   */
  year?: string;
};
export type FirstChoicePredictVo = {
  /**
   * 考生类别
   */
  candidateType?: string;
  /**
   * 总成绩和国家线比较结果描述
   */
  compareLineDesc?: string;
  /**
   * 国家线
   */
  nationalLines?: FirstChoicePredictNationLineVo[];
};
export type Baseresponse_firstchoicepredictvo = {
  code?: number;
  /**
   * FirstChoicePredictVo
   * ---
   */
  data?: FirstChoicePredictVo;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type Baseresponse_list_string = {
  code?: number;
  data?: string[];
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type UniversityMajorDetailVo = {
  /**
   * 院校代码
   */
  code?: string;
  /**
   * 是否已收藏
   */
  collected?: boolean;
  /**
   * 学位类型
   */
  degreeType?: string;
  /**
   * 院系所代码
   */
  departmentCode?: string;
  /**
   * 院系所名称
   */
  departmentName?: string;
  /**
   * 院校简介
   */
  description?: string;
  /**
   * 研究方向代码
   */
  directionCode?: string;
  /**
   * 研究方向名称
   */
  directionName?: string;
  /**
   * 收藏ID
   */
  favId?: string;
  /**
   * 学习方式
   */
  learnType?: string;
  /**
   * 院校名称
   */
  name?: string;
  /**
   * 一级学科代码
   */
  oneSubjectCode?: string;
  /**
   * 一级学科名称
   */
  oneSubjectName?: string;
  /**
   * 电话订阅
   */
  phoneSub?: boolean;
  /**
   * 院校属性
   */
  props?: string[];
  /**
   * 院校地区代码
   */
  provinceCode?: string;
  /**
   * 院校地区名称
   */
  provinceName?: string;
  /**
   * 院校排名
   */
  ranking?: string;
  /**
   * 短信订阅
   */
  smsSub?: boolean;
  /**
   * 学科门类代码
   */
  subjectCategoryCode?: string;
  /**
   * 学科门类名称
   */
  subjectCategoryName?: string;
  /**
   * 二级学科代码
   */
  twoSubjectCode?: string;
  /**
   * 二级学科名称
   */
  twoSubjectName?: string;
  /**
   * 微信消息订阅
   */
  wxSub?: boolean;
};
export type Baseresponse_list_universitymajordetailvo = {
  code?: number;
  data?: UniversityMajorDetailVo[];
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type MajorDetailFilter = {
  /**
   * 学位类型等于
   */
  degreeTypeEquals?: string;
  /**
   * 研究方向代码等于
   */
  directionCodeEquals?: string;
  /**
   * 学习方式等于
   * [required]
   */
  learnTypeEquals: string;
  /**
   * 专业代码等于
   * [required]
   */
  majorCodeEquals: string;
  /**
   * 院校代码等于
   * [required]
   */
  uniCodeEquals: string;
};
export type MajorQueryForRegisterVo = {
  /**
   * 专业代码
   */
  code?: string;
  /**
   * 专业名称
   */
  name?: string;
};
export type Baseresponse_list_majorqueryforregistervo = {
  code?: number;
  data?: MajorQueryForRegisterVo[];
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type MajorQueryForRegisterFilter = {
  /**
   * 专业代码等于或名称类似于
   */
  codeEqualsOrNameLikes?: string;
  /**
   * 院系所代码等于
   */
  departmentCodeEquals?: string;
  /**
   * 学习方式等于
   */
  learnTypeEquals?: string;
  /**
   * 当前页
   */
  page?: number;
  /**
   * 分页条数
   */
  pageSize?: number;
  /**
   * 院校代码等于
   */
  uniCodeEquals?: string;
};
export type ProvinceQdVo = {
  /**
   * 代码
   */
  code?: string;
  /**
   * 名称
   */
  name?: string;
};
export type XsDegreeQdVo = {
  /**
   * 子级
   */
  children?: XsDegreeQdVo[];
  /**
   * 代码
   */
  code?: string;
  /**
   * 名称
   */
  name?: string;
  /**
   * 父代码
   */
  parentCode?: string;
};
export type ZyDegreeQdVo = {
  /**
   * 代码
   */
  code?: string;
  /**
   * 名称
   */
  name?: string;
};
export type QdVo = {
  /**
   * 初试成绩浮动范围值
   */
  firstScoreFloat?: number;
  /**
   * 省份枚举数据
   */
  provinceList?: ProvinceQdVo[];
  /**
   * 学术学位枚举数据
   */
  xsDegreeList?: XsDegreeQdVo[];
  /**
   * 专业学位枚举数据
   */
  zyDegreeList?: ZyDegreeQdVo[];
};
export type Baseresponse_qdvo = {
  code?: number;
  /**
   * QdVo
   * ---
   */
  data?: QdVo;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type UniversityByMajorVo = {
  /**
   * 专业代码
   */
  code?: string;
  /**
   * 专业名称
   */
  name?: string;
  /**
   * 一级学科代码
   */
  oneSubjectCode?: string;
  /**
   * 一级学科名称
   */
  oneSubjectName?: string;
  /**
   * 门类代码
   */
  subjectCategoryCode?: string;
  /**
   * 门类名称
   */
  subjectCategoryName?: string;
};
export type Baseresponse_list_universitybymajorvo = {
  code?: number;
  data?: UniversityByMajorVo[];
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type UniversityByMajorFilter = {
  /**
   * 学习方式等于
   */
  learnTypeEquals?: string;
  /**
   * 专业代码等于
   */
  majorCodeEquals?: string;
  /**
   * 专业代码或名称等于
   */
  majorCodeEqualsOrNameLikes?: string;
  minorityCorePlanEquals?: boolean;
  /**
   * 一级学科代码等于
   */
  oneSubjectCodeEquals?: string;
  /**
   * 当前页
   */
  page?: number;
  /**
   * 分页条数
   */
  pageSize?: number;
  retirePlanEquals?: boolean;
  /**
   * 门类代码等于
   */
  subjectCategoryCodeEquals?: string;
};
export type UniversityByUniVo = {
  /**
   * 院校代码
   */
  code?: string;
  /**
   * 院校简介
   */
  description?: string;
  /**
   * 院校名称
   */
  name?: string;
  /**
   * 院校属性
   */
  props?: string[];
  /**
   * 省份
   */
  provinceName?: string;
  /**
   * 院校排名来源
   */
  rankSource?: string;
  /**
   * 院校排名
   */
  ranking?: string;
  /**
   * 院校类型
   */
  type?: string;
};
export type Baseresponse_list_universitybyunivo = {
  code?: number;
  data?: UniversityByUniVo[];
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type UniversityByUniFilter = {
  /**
   * 学习方式等于
   */
  learnTypeEquals?: string;
  minorityCorePlanEquals?: boolean;
  /**
   * 当前页
   */
  page?: number;
  /**
   * 分页条数
   */
  pageSize?: number;
  /**
   * 院校属性在。。。之间
   */
  propIn?: string[];
  /**
   * 区域代码等于
   */
  provinceCodeEquals?: string;
  /**
   * 省份代码在。。。之间
   */
  provinceCodeIn?: string[];
  retirePlanEquals?: boolean;
  /**
   * 院校代码等于
   */
  uniCodeEquals?: string;
  /**
   * 院校代码或名称等于
   */
  uniCodeEqualsOrNameLikes?: string;
};
export type UniQueryForRegisterVo = {
  /**
   * 院校代码
   */
  code?: string;
  /**
   * 院校名称
   */
  name?: string;
};
export type Baseresponse_list_uniqueryforregistervo = {
  code?: number;
  data?: UniQueryForRegisterVo[];
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type UniQueryForRegisterFilter = {
  /**
   * 院校代码等于或名称类似于
   */
  codeEqualsOrNameLikes?: string;
  /**
   * 学习方式等于
   */
  learnTypeEquals?: string;
  /**
   * 当前页
   */
  page?: number;
  /**
   * 分页条数
   */
  pageSize?: number;
};
export type UniMajorVo = {
  /**
   * 是否已收藏
   */
  collected?: boolean;
  /**
   * 学位类型
   */
  degreeType?: string;
  /**
   * 院系所代码
   */
  departmentCode?: string;
  /**
   * 院系所名称
   */
  departmentName?: string;
  /**
   * 研究方向代码
   */
  directionCode?: string;
  /**
   * 研究方向名称
   */
  directionName?: string;
  /**
   * 学习方式
   */
  learnType?: string;
  /**
   * 专业代码
   */
  majorCode?: string;
  /**
   * 专业名称
   */
  majorName?: string;
  /**
   * 一级学科代码
   */
  oneSubjectCode?: string;
  /**
   * 一级学科名称
   */
  oneSubjectName?: string;
  /**
   * 院校属性
   */
  props?: string[];
  /**
   * 门类代码
   */
  subjectCategoryCode?: string;
  /**
   * 门类名称
   */
  subjectCategoryName?: string;
  /**
   * 招生单位代码
   */
  uniCode?: string;
  /**
   * 招生单位名称
   */
  uniName?: string;
};
export type Baseresponse_list_unimajorvo = {
  code?: number;
  data?: UniMajorVo[];
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type Kdhecfebhb = {
  /**
   * 查询关键字
   */
  keyword?: string;
  /**
   * 近三年有调剂
   */
  last3YearHasAdjust?: boolean;
  /**
   * 学习方式等于
   */
  learnTypeEquals?: string;
  /**
   * 专业代码等于
   */
  majorCodeEquals?: string;
  /**
   * 专业代码在。。。之间
   */
  majorCodeIn?: string[];
  /**
   * 当前页
   */
  page?: number;
  /**
   * 分页条数
   */
  pageSize?: number;
  /**
   * 院校属性在。。。之间
   */
  propIn?: string[];
  /**
   * 省份代码等于
   */
  provinceCodeEquals?: string;
  /**
   * 省份代码在。。。之间
   */
  provinceCodeIn?: string[];
  /**
   * 今年是否发布调剂公告
   */
  publishedAdjust?: boolean;
  /**
   * 院校代码等于
   */
  uniCodeEquals?: string;
};
export type ScoreChartItemVo = {
  /**
   * 录取分数区间
   */
  scorePeriods?: string[];
  /**
   * 分数区间对应的录取人数
   */
  total?: number;
};
export type AdmissionYearScoreRangeAdjustSourceVo = {
  /**
   * 一志愿院校代码
   */
  oneVolunteerUniversityCode?: string;
  /**
   * 一志愿院校名称
   */
  oneVolunteerUniversityName?: string;
  /**
   * 录取人数
   */
  total?: number;
};
export type ScoreChartVo = {
  /**
   * 调剂直方图数据
   */
  adjustItems?: ScoreChartItemVo[];
  /**
   * 调剂来源学校
   */
  adjustSourceList?: AdmissionYearScoreRangeAdjustSourceVo[];
  /**
   * 一志愿直方图数据
   */
  firstChoiceItems?: ScoreChartItemVo[];
  /**
   * 专业是否是新增专业
   */
  majorIsNew?: boolean;
};
export type Baseresponse_scorechartvo = {
  code?: number;
  /**
   * ScoreChartVo
   * ---
   */
  data?: ScoreChartVo;
  message?: string;
  more?: boolean;
  success?: boolean;
  total?: number;
};
export type ScoreChartFilter = {
  /**
   * 学习方式等于
   * [required]
   */
  learnTypeEquals: string;
  /**
   * 专业代码等于
   * [required]
   */
  majorCodeEquals: string;
  /**
   * 院校代码等于
   * [required]
   */
  uniCodeEquals: string;
  /**
   * 年份
   */
  yearEquals?: string;
};
declare global {
  interface Apis {
    xcx: {
      /**
       * ---
       *
       * [DELETE] 删除缓存键值
       *
       * **path:** /monitor/cache/clearCacheKey/{cacheKey}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // cacheKey
       *   // [required]
       *   cacheKey: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = Record<string, object>
       * ```
       */
      clearCacheKeyUsingDELETE<
        Config extends Alova2MethodConfig<Record<string, object>> & {
          pathParams: {
            /**
             * cacheKey
             * [required]
             */
            cacheKey: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<Record<string, object>, 'xcx.clearCacheKeyUsingDELETE', Config>;
      /**
       * ---
       *
       * [GET] 获取缓存键值
       *
       * **path:** /monitor/cache/getValue/{cacheName}/{cacheKey}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // cacheName
       *   // [required]
       *   cacheName: string
       *   // cacheKey
       *   // [required]
       *   cacheKey: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = Record<string, object>
       * ```
       */
      getCacheValueUsingGET<
        Config extends Alova2MethodConfig<Record<string, object>> & {
          pathParams: {
            /**
             * cacheName
             * [required]
             */
            cacheName: string;
            /**
             * cacheKey
             * [required]
             */
            cacheKey: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<Record<string, object>, 'xcx.getCacheValueUsingGET', Config>;
    };
    xcxFeedback: {
      /**
       * ---
       *
       * [POST] 提交意见反馈
       *
       * **path:** /xcx/feedback/create
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 用户联系方式，如邮箱地址、电话号码等
       *   contactInfo?: string
       *   // 意见内容，详细描述用户遇到的问题或者提出的建议
       *   content?: string
       *   // 意见图片
       *   images?: string[]
       *   // 意见类别: BUG:Bug, CORRECT:数据纠错, ADVISE:建议, COMPLAINT:吐槽, OTHER:其他
       *   type?: 'ADVISE' | 'BUG' | 'COMPLAINT' | 'CORRECT' | 'OTHER'
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      createUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_void> & {
          data: FeedBackRequest;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_void, 'xcxFeedback.createUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 意见反馈列表
       *
       * **path:** /xcx/feedback/list
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 当前页
       *   page?: number
       *   // 分页条数
       *   pageSize?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: Array<{
       *     // 用户联系方式，如邮箱地址、电话号码等
       *     contactInfo?: string
       *     // 意见内容，详细描述用户遇到的问题或者提出的建议
       *     content?: string
       *     // 创建时间
       *     created?: string
       *     // 意见图片
       *     images?: string[]
       *     // 意见类别，例如：Bug, 数据纠错, 建议, 吐槽、其他等
       *     type?: string
       *   }>
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      listUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_list_feedbackvo> & {
          data: FeedBackFilter;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_list_feedbackvo, 'xcxFeedback.listUsingPOST', Config>;
    };
    xcxMember: {
      /**
       * ---
       *
       * [POST] 修改头像
       *
       * **path:** /xcx/member/avatar/update
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = Blob
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: string
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      updateAvatarUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_string> & {
          data: Blob;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_string, 'xcxMember.updateAvatarUsingPOST', Config>;
      /**
       * ---
       *
       * [GET] 付费会员H5购买链接
       *
       * **path:** /xcx/member/buy/pay/h5
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // unionid
       *   unionid?: string
       *   // openid
       *   openid?: string
       *   // couponCode
       *   couponCode?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = unknown
       * ```
       */
      payH5UsingGET<
        Config extends Alova2MethodConfig<unknown> & {
          params: {
            /**
             * unionid
             */
            unionid?: string;
            /**
             * openid
             */
            openid?: string;
            /**
             * couponCode
             */
            couponCode?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<unknown, 'xcxMember.payH5UsingGET', Config>;
      /**
       * ---
       *
       * [POST] 购买付费会员预下单
       *
       * **path:** /xcx/member/buy/payPreCreate
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 福利码
       *   couponCode?: string
       *   // OPENID
       *   // [required]
       *   openid: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   // [title] MemberBuyPreOrderResult
       *   data?: {
       *     // 购买记录的内部订单ID
       *     // [required]
       *     orderId: number
       *     // 小程序发起预下单的商户订单号
       *     // [required]
       *     orderNumber: string
       *     // 微信返回的用于唤起小程序支付的参数
       *     // [required]
       *     payInfo: string
       *   }
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      payPreCreateUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_memberbuypreorderresult> & {
          data: Kfbhddgdhc;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_memberbuypreorderresult, 'xcxMember.payPreCreateUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 购买付费会员支付结果查询
       *
       * **path:** /xcx/member/buy/payQuery
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 订单ID
       *   // [required]
       *   orderId: number
       *   // 支付商户订单号,前端生成
       *   // [required]
       *   orderNumber: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   // [title] MemberBuyPayResult
       *   data?: {
       *     // 购买记录的内部订单ID
       *     // [required]
       *     orderId: number
       *     // 小程序发起预下单的商户订单号
       *     // [required]
       *     orderNumber: string
       *     // 支付结果: 0 成功 1失败 2支付中
       *     // [required]
       *     payState: number
       *   }
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      payQueryUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_memberbuypayresult> & {
          data: Kbgbgeebef;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_memberbuypayresult, 'xcxMember.payQueryUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 购买付费会员价格
       *
       * **path:** /xcx/member/buy/price
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   // [title] MemberBuyPrice
       *   data?: {
       *     // 福利码优惠后价格
       *     // [required]
       *     couponPrice: number
       *     // 标准价格
       *     // [required]
       *     rtlPrice: number
       *     // 虚拟原价
       *     // [required]
       *     virtualPrice: number
       *   }
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      getPriceUsingPOST<Config extends Alova2MethodConfig<Baseresponse_memberbuyprice>>(
        config?: Config
      ): Alova2Method<Baseresponse_memberbuyprice, 'xcxMember.getPriceUsingPOST', Config>;
      /**
       * ---
       *
       * [GET] 是否可弹框
       *
       * **path:** /xcx/member/canPopup
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: boolean
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      canPopupUsingGET<Config extends Alova2MethodConfig<Baseresponse_boolean>>(
        config?: Config
      ): Alova2Method<Baseresponse_boolean, 'xcxMember.canPopupUsingGET', Config>;
      /**
       * ---
       *
       * [POST] 获取用户openId
       *
       * **path:** /xcx/member/code2Session
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 凭据
       *   code?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   // [title] 微信用户信息
       *   data?: {
       *     // 微信用户openId
       *     openid?: string
       *     // 会话密钥
       *     sessionKey?: string
       *     // 微信用户unionid
       *     unionid?: string
       *   }
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      code2SessionUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse> & {
          data: Code2SessionRequest;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse, 'xcxMember.code2SessionUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 添加福利码
       *
       * **path:** /xcx/member/coupon/add
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 福利码
       *   // [required]
       *   couponCode: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      addCouponUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_void> & {
          data: Bgbfhjgacd;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_void, 'xcxMember.addCouponUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 我的福利码列表
       *
       * **path:** /xcx/member/coupon/list
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = object
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: Array<{
       *     // 福利码
       *     // [required]
       *     couponCode: string
       *     // 领取时间
       *     // [required]
       *     created: string
       *     // 会员ID
       *     // [required]
       *     memberId: string
       *   }>
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      listCouponUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_list_membercouponresult> & {
          data: Bebgdbjddb;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_list_membercouponresult, 'xcxMember.listCouponUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 解密微信
       *
       * **path:** /xcx/member/decryptWx
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   data?: string
       *   iv?: string
       *   key?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: string
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      decryptWxUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_string> & {
          data: DecryptRequest;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_string, 'xcxMember.decryptWxUsingPOST', Config>;
      /**
       * ---
       *
       * [GET] 获取用户的当日免费额度
       *
       * **path:** /xcx/member/freeuage/count
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: number
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      freeUsageCountUsingGET<Config extends Alova2MethodConfig<Baseresponse_int>>(
        config?: Config
      ): Alova2Method<Baseresponse_int, 'xcxMember.freeUsageCountUsingGET', Config>;
      /**
       * ---
       *
       * [POST] 会员信息
       *
       * **path:** /xcx/member/info
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   // [title] 会员
       *   data?: {
       *     // 头像
       *     avatar?: string
       *     // 业务课一代码
       *     biz1Code?: string
       *     // 业务课一名称
       *     biz1Name?: string
       *     // 业务课一成绩
       *     biz1Score?: number
       *     // 业务课二代码
       *     biz2Code?: string
       *     // 业务课二名称
       *     biz2Name?: string
       *     // 业务课二成绩
       *     biz2Score?: number
       *     // 院系所代码
       *     departmentCode?: string
       *     // 院系所名称
       *     departmentName?: string
       *     // 研究方向代码
       *     directionCode?: string
       *     // 研究方向名称
       *     directionName?: string
       *     // 准考证号
       *     examRegistrationNumber?: string
       *     // 一志愿专业代码
       *     firstChoiceDegreeCode?: string
       *     // 一志愿专业名称
       *     firstChoiceDegreeName?: string
       *     // 一志愿学校代码
       *     firstChoiceSchoolCode?: string
       *     // 一志愿学校名称
       *     firstChoiceSchoolName?: string
       *     // 初试成绩
       *     firstScore?: number
       *     // 外语课代码
       *     foreignLanguageCode?: string
       *     // 外语课名称
       *     foreignLanguageName?: string
       *     // 外语成绩
       *     foreignLanguageScore?: number
       *     // 会员ID
       *     id?: string
       *     // 会员手机号
       *     mobile?: string
       *     // 会员名称
       *     name?: string
       *     // 会员昵称
       *     nickName?: string
       *     // 会员微信OPENID
       *     openid?: string
       *     // 政治课代码
       *     politicsCode?: string
       *     // 政治课名称
       *     politicsName?: string
       *     // 政治成绩
       *     politicsScore?: number
       *     // 全日志1/非全日制0
       *     studyType?: string
       *     // 普通用户0/付费用户1
       *     type?: number
       *     // 会员微信unionid
       *     unionid?: string
       *   }
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      infoUsingPOST<Config extends Alova2MethodConfig<Baseresponse1>>(
        config?: Config
      ): Alova2Method<Baseresponse1, 'xcxMember.infoUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 登录成功后，签发的令牌中会包含用户的openid信息
       *
       * **path:** /xcx/member/login
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 会员登录的微信小程序openid
       *   openid?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   // [title] 会员登录结果
       *   data?: {
       *     // [title] 会员
       *     member?: {
       *       // 头像
       *       avatar?: string
       *       // 业务课一代码
       *       biz1Code?: string
       *       // 业务课一名称
       *       biz1Name?: string
       *       // 业务课一成绩
       *       biz1Score?: number
       *       // 业务课二代码
       *       biz2Code?: string
       *       // 业务课二名称
       *       biz2Name?: string
       *       // 业务课二成绩
       *       biz2Score?: number
       *       // 院系所代码
       *       departmentCode?: string
       *       // 院系所名称
       *       departmentName?: string
       *       // 研究方向代码
       *       directionCode?: string
       *       // 研究方向名称
       *       directionName?: string
       *       // 准考证号
       *       examRegistrationNumber?: string
       *       // 一志愿专业代码
       *       firstChoiceDegreeCode?: string
       *       // 一志愿专业名称
       *       firstChoiceDegreeName?: string
       *       // 一志愿学校代码
       *       firstChoiceSchoolCode?: string
       *       // 一志愿学校名称
       *       firstChoiceSchoolName?: string
       *       // 初试成绩
       *       firstScore?: number
       *       // 外语课代码
       *       foreignLanguageCode?: string
       *       // 外语课名称
       *       foreignLanguageName?: string
       *       // 外语成绩
       *       foreignLanguageScore?: number
       *       // 会员ID
       *       id?: string
       *       // 会员手机号
       *       mobile?: string
       *       // 会员名称
       *       name?: string
       *       // 会员昵称
       *       nickName?: string
       *       // 会员微信OPENID
       *       openid?: string
       *       // 政治课代码
       *       politicsCode?: string
       *       // 政治课名称
       *       politicsName?: string
       *       // 政治成绩
       *       politicsScore?: number
       *       // 全日志1/非全日制0
       *       studyType?: string
       *       // 普通用户0/付费用户1
       *       type?: number
       *       // 会员微信unionid
       *       unionid?: string
       *     }
       *   }
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      loginUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse2> & {
          data: Kbjgjejfbia;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse2, 'xcxMember.loginUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 修改昵称
       *
       * **path:** /xcx/member/nickname/update
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 昵称
       *   nickName?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      updateNicknameUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_void> & {
          data: Bbfgjbbbif;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_void, 'xcxMember.updateNicknameUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 修改个人信息
       *
       * **path:** /xcx/member/profile/update
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 业务课一代码
       *   biz1Code?: string
       *   // 业务课一名称
       *   biz1Name?: string
       *   // 业务课一成绩
       *   biz1Score?: number
       *   // 业务课二代码
       *   biz2Code?: string
       *   // 业务课二名称
       *   biz2Name?: string
       *   // 业务课二成绩
       *   biz2Score?: number
       *   // 院系所代码
       *   departmentCode?: string
       *   // 院系所名称
       *   departmentName?: string
       *   // 研究方向代码
       *   directionCode?: string
       *   // 研究方向名称
       *   directionName?: string
       *   // 准考证号
       *   examRegistrationNumber?: string
       *   // 一志愿专业代码
       *   firstChoiceDegreeCode?: string
       *   // 一志愿专业名称
       *   firstChoiceDegreeName?: string
       *   // 一志愿学校代码
       *   firstChoiceSchoolCode?: string
       *   // 一志愿学校名称
       *   firstChoiceSchoolName?: string
       *   // 初试成绩
       *   firstScore?: number
       *   // 外语课代码
       *   foreignLanguageCode?: string
       *   // 外语课名称
       *   foreignLanguageName?: string
       *   // 外语成绩
       *   foreignLanguageScore?: number
       *   // 会员ID
       *   memberId?: string
       *   // 政治课代码
       *   politicsCode?: string
       *   // 政治课名称
       *   politicsName?: string
       *   // 政治成绩
       *   politicsScore?: number
       *   // 全日制1/非全日制0
       *   studyType?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      updateProfileUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_void> & {
          data: Kjceaejjbh;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_void, 'xcxMember.updateProfileUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 记录弹框次数
       *
       * **path:** /xcx/member/recordPopTimes
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      recordPopTimesUsingPOST<Config extends Alova2MethodConfig<Baseresponse_void>>(
        config?: Config
      ): Alova2Method<Baseresponse_void, 'xcxMember.recordPopTimesUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 注册
       *
       * **path:** /xcx/member/register
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 会员手机号
       *   // [required]
       *   mobile: string
       *   // 会员昵称
       *   // [required]
       *   nickName: string
       *   // 会员微信OPENID
       *   // [required]
       *   openid: string
       *   // 会员微信unionid
       *   // [required]
       *   unionid: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   // [title] 会员登录结果
       *   data?: {
       *     // [title] 会员
       *     member?: {
       *       // 头像
       *       avatar?: string
       *       // 业务课一代码
       *       biz1Code?: string
       *       // 业务课一名称
       *       biz1Name?: string
       *       // 业务课一成绩
       *       biz1Score?: number
       *       // 业务课二代码
       *       biz2Code?: string
       *       // 业务课二名称
       *       biz2Name?: string
       *       // 业务课二成绩
       *       biz2Score?: number
       *       // 院系所代码
       *       departmentCode?: string
       *       // 院系所名称
       *       departmentName?: string
       *       // 研究方向代码
       *       directionCode?: string
       *       // 研究方向名称
       *       directionName?: string
       *       // 准考证号
       *       examRegistrationNumber?: string
       *       // 一志愿专业代码
       *       firstChoiceDegreeCode?: string
       *       // 一志愿专业名称
       *       firstChoiceDegreeName?: string
       *       // 一志愿学校代码
       *       firstChoiceSchoolCode?: string
       *       // 一志愿学校名称
       *       firstChoiceSchoolName?: string
       *       // 初试成绩
       *       firstScore?: number
       *       // 外语课代码
       *       foreignLanguageCode?: string
       *       // 外语课名称
       *       foreignLanguageName?: string
       *       // 外语成绩
       *       foreignLanguageScore?: number
       *       // 会员ID
       *       id?: string
       *       // 会员手机号
       *       mobile?: string
       *       // 会员名称
       *       name?: string
       *       // 会员昵称
       *       nickName?: string
       *       // 会员微信OPENID
       *       openid?: string
       *       // 政治课代码
       *       politicsCode?: string
       *       // 政治课名称
       *       politicsName?: string
       *       // 政治成绩
       *       politicsScore?: number
       *       // 全日志1/非全日制0
       *       studyType?: string
       *       // 普通用户0/付费用户1
       *       type?: number
       *       // 会员微信unionid
       *       unionid?: string
       *     }
       *   }
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      registerUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse2> & {
          data: Kcaefjaecja;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse2, 'xcxMember.registerUsingPOST', Config>;
      /**
       * ---
       *
       * [GET] 获取用户剩余更新次数
       *
       * **path:** /xcx/member/remainUpdateTimes
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: number
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      remainUpdateTimesUsingGET<Config extends Alova2MethodConfig<Baseresponse_int>>(
        config?: Config
      ): Alova2Method<Baseresponse_int, 'xcxMember.remainUpdateTimesUsingGET', Config>;
      /**
       * ---
       *
       * [POST] 会员权益说明
       *
       * **path:** /xcx/member/rights
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   // [title] MemberRightsVo
       *   data?: {
       *     // 轮播图
       *     bannerPics?: Array<{
       *       // 子图
       *       childrenPics?: string[]
       *       // 主图
       *       masterPic?: string
       *     }>
       *     // 客服二维码
       *     customerServiceQrcode?: string
       *     // [title] MasterSlavePic
       *     popupPic?: MasterSlavePic
       *     // 会员权益说明图片
       *     rightsPic?: string
       *     // 用户手册
       *     userGuide?: string
       *   }
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      rightsUsingPOST<Config extends Alova2MethodConfig<Baseresponse_memberrightsvo>>(
        config?: Config
      ): Alova2Method<Baseresponse_memberrightsvo, 'xcxMember.rightsUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 立即执行启动过期任务
       *
       * **path:** /xcx/member/startExpireJob
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      startExpireJobUsingPOST<Config extends Alova2MethodConfig<Baseresponse_void>>(
        config?: Config
      ): Alova2Method<Baseresponse_void, 'xcxMember.startExpireJobUsingPOST', Config>;
    };
    xcxNews: {
      /**
       * ---
       *
       * [POST] 公告查询
       *
       * **path:** /xcx/news/query
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 学校/专业/代码搜索
       *   keyword?: string
       *   // 学习方式等于
       *   learnTypeEquals?: string
       *   // 专业代码等于
       *   majorCodeEquals?: string
       *   // 专业代码在。。。之间
       *   majorCodeIn?: string[]
       *   // 会员ID
       *   memberId?: string
       *   page?: number
       *   pageSize?: number
       *   // 院校属性在。。。之间
       *   propIn?: string[]
       *   // 省份代码等于
       *   provinceCodeEquals?: string
       *   // 省份代码在。。。之间
       *   provinceCodeIn?: string[]
       *   // 院校代码等于
       *   uniCodeEquals?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: Array<{
       *     // 公告内容
       *     content?: string
       *     // 创建时间
       *     created?: string
       *     degreeType?: string
       *     // 院系所代码
       *     departmentCode?: string
       *     // 院系所名称
       *     departmentName?: string
       *     learnType?: string
       *     // 公告外部链接
       *     linkUrl?: string
       *     // 专业代码
       *     majorCode?: string
       *     // 专业名称
       *     majorName?: string
       *     // 来源渠道
       *     source?: string
       *     // 公告标题
       *     title?: string
       *     // 招生单位代码
       *     unitCode?: string
       *     // 招生单位名称
       *     unitName?: string
       *   }>
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      queryUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_list_news> & {
          data: NewsFilter;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_list_news, 'xcxNews.queryUsingPOST', Config>;
    };
    xcxUniversity: {
      /**
       * ---
       *
       * [POST] 调剂分析结果
       *
       * **path:** /xcx/university/adjust/analysis/result
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   // [title] AdjustAnalysisResultVo
       *   data?: {
       *     // 可参加调剂的院校数量
       *     adjustUniTotal?: number
       *     // 考生类别
       *     candidateType?: string
       *     // 不区分A、B区的所有调剂院校专业信息
       *     details?: Array<{
       *       // 学位类型
       *       degreeType?: string
       *       // 学习方式
       *       learnType?: string
       *       // 专业代码
       *       majorCode?: string
       *       // 专业名称
       *       majorName?: string
       *       // 一级学科代码
       *       oneSubjectCode?: string
       *       // 一级学科名称
       *       oneSubjectName?: string
       *       // 院校属性
       *       props?: string[]
       *       // 院校所属区（A、B）
       *       provinceType?: string
       *       // 门类代码
       *       subjectCategoryCode?: string
       *       // 门类名称
       *       subjectCategoryName?: string
       *       // 院校代码
       *       uniCode?: string
       *       // 院校名称
       *       uniName?: string
       *     }>
       *     // A区可调剂院校专业信息
       *     detailsA?: Array<AdjustAnalysisResultDetailVo>
       *     // B区可调剂院校专业信息
       *     detailsB?: Array<AdjustAnalysisResultDetailVo>
       *     // 是否大于等于A区国家线
       *     gteA?: boolean
       *     // 同类型的院校数量
       *     sameTypeUniTotal?: number
       *   }
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      adjustAnalysisResultUsingPOST<Config extends Alova2MethodConfig<Baseresponse_adjustanalysisresultvo>>(
        config?: Config
      ): Alova2Method<Baseresponse_adjustanalysisresultvo, 'xcxUniversity.adjustAnalysisResultUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 同分去向数据总览
       *
       * **path:** /xcx/university/adjust/analysis/sameScore/dest
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = object
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   // [title] SameScoreDest4AdjustAnalysisChartVo
       *   data?: {
       *     items?: Array<{
       *       // 院校属性及其对应的院校数量
       *       details?: Array<{
       *         // 院校属性对应数量
       *         count?: number
       *         // 院校属性
       *         prop?: string
       *       }>
       *       // 年份
       *       year?: string
       *     }>
       *   }
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      sameScoreDest4AdjustAnalysisUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_samescoredest4adjustanalysischartvo> & {
          data: SameScoreDest4AdjustAnalysisFilter;
        }
      >(
        config: Config
      ): Alova2Method<
        Baseresponse_samescoredest4adjustanalysischartvo,
        'xcxUniversity.sameScoreDest4AdjustAnalysisUsingPOST',
        Config
      >;
      /**
       * ---
       *
       * [POST] 同校去向数据总览
       *
       * **path:** /xcx/university/adjust/analysis/sameUni/dest
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = object
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   // [title] SameUniDest4AdjustAnalysisChartVo
       *   data?: {
       *     items?: Array<{
       *       // 院校属性及其对应的院校数量
       *       details?: Array<{
       *         // 院校属性对应数量
       *         count?: number
       *         // 院校属性
       *         prop?: string
       *       }>
       *       // 年份
       *       year?: string
       *     }>
       *   }
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      sameUniDest4AdjustAnalysisUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_sameunidest4adjustanalysischartvo> & {
          data: SameUniDest4AdjustAnalysisFilter;
        }
      >(
        config: Config
      ): Alova2Method<
        Baseresponse_sameunidest4adjustanalysischartvo,
        'xcxUniversity.sameUniDest4AdjustAnalysisUsingPOST',
        Config
      >;
      /**
       * ---
       *
       * [POST] 招生单位基本信息
       *
       * **path:** /xcx/university/admission/base
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 学位类型等于
       *   degreeTypeEquals?: string
       *   // 研究方向代码
       *   directionCodeEquals?: string
       *   // 学习方式等于
       *   // [required]
       *   learnTypeEquals: string
       *   // 专业代码等于
       *   // [required]
       *   majorCodeEquals: string
       *   // 招生单位代码等于
       *   // [required]
       *   unitCodeEquals: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   // [title] AdmissionBaseVo
       *   data?: {
       *     // 招生明细
       *     admissionItems?: Array<{
       *       // 学位类型
       *       degreeType?: string
       *       // 研究方向代码
       *       directionCode?: string
       *       // 研究方向名称
       *       directionName?: string
       *       // 考试科目（四门业务课）
       *       examList?: Array<{
       *         // 业务课一代码
       *         bizCourseOneCode?: string
       *         // 业务课一名称
       *         bizCourseOneName?: string
       *         // 业务课二代码
       *         bizCourseTwoCode?: string
       *         // 业务课二名称
       *         bizCourseTwoName?: string
       *         // 外语课代码
       *         foreignLanguageCode?: string
       *         // 外语课名称
       *         foreignLanguageName?: string
       *         // 政治课代码
       *         politicsCode?: string
       *         // 政治课名称
       *         politicsName?: string
       *       }>
       *       // 指导教师
       *       guideTeacher?: string
       *       // 学习方式
       *       learnType?: string
       *       // 是否少数民族骨干计划
       *       minorityCorePlan?: boolean
       *       // 拟招生人数
       *       proposedAdmissionNum?: number
       *       // [title] ReferBookVo
       *       referBook?: {
       *         // 作者
       *         author?: string
       *         // 书名
       *         name?: string
       *         // 出版社
       *         publishHouse?: string
       *         // 出版年份
       *         year?: string
       *       }
       *       // 是否退役计划
       *       retirePlan?: boolean
       *       // 复试科目
       *       secondCourseList?: Array<{
       *         // 复试科目代码
       *         code?: string
       *         // 复试科目名称
       *         name?: string
       *       }>
       *     }>
       *   }
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      queryAdmissionBaseUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_admissionbasevo> & {
          data: AdmissionBaseFilter;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_admissionbasevo, 'xcxUniversity.queryAdmissionBaseUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 近三年的录取人数
       *
       * **path:** /xcx/university/admission/last3Years
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 学位类型等于
       *   degreeTypeEquals?: string
       *   // 学习方式等于
       *   learnTypeEquals?: string
       *   // 专业代码等于
       *   majorCodeEquals?: string
       *   // 招生单位代码等于
       *   uniCodeEquals?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: Array<{
       *     // 调剂录取人数
       *     adjust?: number
       *     // 一志愿录取人数
       *     oneVolunteer?: number
       *     // 录取总数
       *     total?: number
       *     // 年份
       *     year?: string
       *   }>
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      queryMajorDetailUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_list_admissionlast3yearsvo> & {
          data: AdmissionLast3YearsFilter;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_list_admissionlast3yearsvo, 'xcxUniversity.queryMajorDetailUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 近三年录取分数区间
       *
       * **path:** /xcx/university/admission/last3YearsScoreRange
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 学位类型等于
       *   degreeTypeEquals?: string
       *   // 学习方式等于
       *   learnTypeEquals?: string
       *   // 专业代码等于
       *   majorCodeEquals?: string
       *   // 招生单位代码等于
       *   uniCodeEquals?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: Array<{
       *     // 录取调整最高分
       *     adjustMax?: number
       *     // 录取调整最低分
       *     adjustMin?: number
       *     // 一志愿录取最低分
       *     firstChoiceMax?: number
       *     // 一志愿录取最低分
       *     firstChoiceMin?: number
       *     // 年份
       *     year?: string
       *   }>
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      queryLast3YearsScoreRangeUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_list_admissionlast3yearsscorerangevo> & {
          data: AdmissionLast3YearsScoreRangeFilter;
        }
      >(
        config: Config
      ): Alova2Method<
        Baseresponse_list_admissionlast3yearsscorerangevo,
        'xcxUniversity.queryLast3YearsScoreRangeUsingPOST',
        Config
      >;
      /**
       * ---
       *
       * [POST] 拟录取名单
       *
       * **path:** /xcx/university/admission/proposed/name
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 学位类型等于
       *   degreeTypeEquals?: string
       *   // 学习方式等于
       *   learnTypeEquals?: string
       *   // 专业代码等于
       *   majorCodeEquals?: string
       *   // 当前页
       *   page?: number
       *   // 分页条数
       *   pageSize?: number
       *   // 招生单位代码等于
       *   uniCodeEquals?: string
       *   // 年份在。。。之间
       *   // [required]
       *   yearsIn: string[]
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   // [title] AdmissionProposedName4AnalysisVo
       *   data?: {
       *     // 拟录取名单数据
       *     admPnList?: Array<{
       *       // 录取方式
       *       admissionType?: string
       *       // 业务课一成绩
       *       bizOne?: number
       *       // 业务课二成绩
       *       bizTwo?: number
       *       // 初试成绩
       *       first?: number
       *       // 初试成绩排名
       *       firstRank?: number
       *       // 外语成绩
       *       foreign?: number
       *       // 姓名
       *       name?: string
       *       // 一志愿院校代码
       *       oneUniversityCode?: string
       *       // 一志愿院校名称
       *       oneUniversityName?: string
       *       // 政治成绩
       *       politics?: number
       *       // 复试成绩
       *       second?: number
       *       // 总成绩
       *       total?: number
       *       // 年份
       *       year?: string
       *     }>
       *     // 专业是否新增专业
       *     majorIsNew?: boolean
       *   }
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      queryProposedNameUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_admissionproposedname4analysisvo> & {
          data: AdmissionProposedNameFilter;
        }
      >(
        config: Config
      ): Alova2Method<
        Baseresponse_admissionproposedname4analysisvo,
        'xcxUniversity.queryProposedNameUsingPOST',
        Config
      >;
      /**
       * ---
       *
       * [POST] 院校专业招生专业目录列表
       *
       * **path:** /xcx/university/admission_major_catalog/query
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 院系所代码等于
       *   departmentCodeEquals?: string
       *   // 研究方向代码等于
       *   directionCodeEquals?: string
       *   // 招生专业目录主键等于
       *   idEquals?: string
       *   // 学习方式等于
       *   learnTypeEquals?: string
       *   // 专业代码等于
       *   majorCodeEquals?: string
       *   // 当前页
       *   page?: number
       *   // 分页条数
       *   pageSize?: number
       *   // 院校属性在。。。之间
       *   propIn?: string[]
       *   // 省份代码等于
       *   provinceCodeEquals?: string
       *   // 省份代码在。。。之间
       *   provinceCodeIn?: string[]
       *   // 招生单位代码等于
       *   uniCodeEquals?: string
       *   // 当年
       *   year?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: Array<{
       *     // 业务课一代码
       *     bizCourseOneCode?: string
       *     // 业务课一名称
       *     bizCourseOneName?: string
       *     // 业务课二代码
       *     bizCourseTwoCode?: string
       *     // 业务课二名称
       *     bizCourseTwoName?: string
       *     // 学位类型
       *     degreeType?: string
       *     // 院系所代码
       *     departmentCode?: string
       *     // 院系所名称
       *     departmentName?: string
       *     // 研究方向代码
       *     directionCode?: string
       *     // 研究方向名称
       *     directionName?: string
       *     // 考试方式
       *     examType?: string
       *     // 外语课代码
       *     foreignLanguageCode?: string
       *     // 外语课名称
       *     foreignLanguageName?: string
       *     // 学习方式
       *     learnType?: string
       *     // 专业代码
       *     majorCode?: string
       *     // 专业名称
       *     majorName?: string
       *     // 政治课代码
       *     politicsCode?: string
       *     // 政治课名称
       *     politicsName?: string
       *     // 拟招生人数
       *     proposedAdmissionNum?: number
       *     // 是否发布调剂公告
       *     publishedAdjustNotice?: boolean
       *     // 招生单位代码
       *     unitCode?: string
       *     // 招生单位名称
       *     unitName?: string
       *   }>
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      queryAdmissionMajorCatalogUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_list_admissionmajorcatalogvo> & {
          data: AdmissionMajorCatalogFilter;
        }
      >(
        config: Config
      ): Alova2Method<
        Baseresponse_list_admissionmajorcatalogvo,
        'xcxUniversity.queryAdmissionMajorCatalogUsingPOST',
        Config
      >;
      /**
       * ---
       *
       * [POST] 获取代码名称配置
       *
       * **path:** /xcx/university/codeName/list
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 配置类型
       *   type?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: Array<{
       *     // 代码
       *     code?: string
       *     // 名称
       *     name?: string
       *   }>
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      listCodeNameUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_list_codenamevo> & {
          data: CodeNameFilter;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_list_codenamevo, 'xcxUniversity.listCodeNameUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 院校对比
       *
       * **path:** /xcx/university/compare
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // [title] CompareItemFilter
       *   // [required]
       *   source: {
       *     // 学位类型等于
       *     // [required]
       *     degreeTypeEquals: string
       *     // 学习方式等于
       *     // [required]
       *     learnTypeEquals: string
       *     // 专业代码等于
       *     // [required]
       *     majorCodeEquals: string
       *     // 院校代码等于
       *     // [required]
       *     uniCodeEquals: string
       *   }
       *   // [title] CompareItemFilter
       *   // [required]
       *   target: CompareItemFilter
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   // [title] CompareResultVo
       *   data?: {
       *     // [title] CompareResultItemVo
       *     source?: {
       *       // [title] AdmissionAdjustMaxScoreVo
       *       adjustMax?: {
       *         items?: Array<{
       *           adjustMax?: number
       *           year?: string
       *         }>
       *       }
       *       // [title] AdmissionAdjustMinScoreVo
       *       adjustMin?: {
       *         items?: Array<{
       *           adjustMin?: number
       *           year?: string
       *         }>
       *       }
       *       // [title] AdjustNumVo
       *       adjustNum?: {
       *         items?: Array<{
       *           adjustNum?: number
       *           year?: string
       *         }>
       *       }
       *       // 调剂来源最多的院校(近四年)
       *       adjustSourceUniList?: Array<{
       *         oneVolunteerUniversityCode?: string
       *         oneVolunteerUniversityName?: string
       *         total?: number
       *       }>
       *       // 招生人数
       *       admissionNumInfoList?: Array<{
       *         // 招生人数
       *         admissionNum?: number
       *         // 招生类型
       *         type?: string
       *       }>
       *       // 学位类型等于
       *       degreeType?: string
       *       // [title] AdmissionFirstChoiceMaxScoreVo
       *       firstChoiceMax?: {
       *         items?: Array<{
       *           firstChoiceMax?: number
       *           year?: string
       *         }>
       *       }
       *       // [title] AdmissionFirstChoiceMinScoreVo
       *       firstChoiceMin?: {
       *         items?: Array<{
       *           firstChoiceMin?: number
       *           year?: string
       *         }>
       *       }
       *       // 初试科目
       *       firstExamList?: Array<{
       *         // 业务课一代码
       *         bizCourseOneCode?: string
       *         // 业务课一名称
       *         bizCourseOneName?: string
       *         // 业务课二代码
       *         bizCourseTwoCode?: string
       *         // 业务课二名称
       *         bizCourseTwoName?: string
       *         // 外语课代码
       *         foreignLanguageCode?: string
       *         // 外语课名称
       *         foreignLanguageName?: string
       *         // 政治课代码
       *         politicsCode?: string
       *         // 政治课名称
       *         politicsName?: string
       *       }>
       *       // 学习方式
       *       learnType?: string
       *       // 专业代码
       *       majorCode?: string
       *       // 专业名称
       *       majorName?: string
       *       // 学校排名
       *       rank?: string
       *       // 推荐类型
       *       recommendType?: string
       *       // 院校代码
       *       uniCode?: string
       *       // 院校名称
       *       uniName?: string
       *     }
       *     // [title] CompareResultItemVo
       *     target?: CompareResultItemVo
       *   }
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      compareUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_compareresultvo> & {
          data: CompareFilter;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_compareresultvo, 'xcxUniversity.compareUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 注册会员选择院系所
       *
       * **path:** /xcx/university/department/queryForRegister
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 院系所代码等于或名称类似于
       *   codeEqualsOrNameLikes?: string
       *   // 学习方式等于
       *   learnTypeEquals?: string
       *   // 当前页
       *   page?: number
       *   // 分页条数
       *   pageSize?: number
       *   // 院校代码等于
       *   uniCodeEquals?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: Array<{
       *     // 院系所代码
       *     code?: string
       *     // 院系所名称
       *     name?: string
       *   }>
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      queryDepartmentForRegisterUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_list_departmentqueryforregistervo> & {
          data: DepartmentQueryForRegisterFilter;
        }
      >(
        config: Config
      ): Alova2Method<
        Baseresponse_list_departmentqueryforregistervo,
        'xcxUniversity.queryDepartmentForRegisterUsingPOST',
        Config
      >;
      /**
       * ---
       *
       * [POST] 注册会员选择研究方向
       *
       * **path:** /xcx/university/direction/queryForRegister
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 研究方向代码等于或名称类似于
       *   codeEqualsOrNameLikes?: string
       *   // 院系所代码等于
       *   departmentCodeEquals?: string
       *   // 学习方式等于
       *   learnTypeEquals?: string
       *   // 专业代码等于
       *   majorCodeEquals?: string
       *   // 当前页
       *   page?: number
       *   // 分页条数
       *   pageSize?: number
       *   // 院校代码等于
       *   uniCodeEquals?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: Array<{
       *     // 研究方向代码
       *     code?: string
       *     // 研究方向名称
       *     name?: string
       *   }>
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      queryDirectionForRegisterUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_list_directionqueryforregistervo> & {
          data: DirectionQueryForRegisterFilter;
        }
      >(
        config: Config
      ): Alova2Method<
        Baseresponse_list_directionqueryforregistervo,
        'xcxUniversity.queryDirectionForRegisterUsingPOST',
        Config
      >;
      /**
       * ---
       *
       * [POST] 收藏
       *
       * **path:** /xcx/university/fav/collect
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 学位类型: 1：学术学位、2：专业学位
       *   degreeType?: string
       *   // 学习方式: 1：全日制、0：非全日制
       *   learnType?: string
       *   // 专业代码
       *   majorCode?: string
       *   // 学院代码
       *   uniCode?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      collectUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_void> & {
          data: MyCollectRequest;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_void, 'xcxUniversity.collectUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 收藏列表
       *
       * **path:** /xcx/university/fav/list
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 当前页
       *   page?: number
       *   // 分页条数
       *   pageSize?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: Array<{
       *     // 学位类型
       *     degreeType?: string
       *     // 主键
       *     id?: string
       *     // 学习方式
       *     learnType?: string
       *     // 专业代码
       *     majorCode?: string
       *     // 专业名称
       *     majorName?: string
       *     // 电话订阅
       *     phoneSub?: boolean
       *     // 院校属性
       *     props?: string[]
       *     // 短信订阅
       *     smsSub?: boolean
       *     // 院校代码
       *     uniCode?: string
       *     // 院校名称
       *     uniName?: string
       *     // 微信消息订阅
       *     wxSub?: boolean
       *   }>
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      favListUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_list_mycollectvo> & {
          data: MyCollectFilter;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_list_mycollectvo, 'xcxUniversity.favListUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 订阅调剂公告通知
       *
       * **path:** /xcx/university/fav/subscribe
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 收藏表主键
       *   id?: number
       *   // 收藏类型
       *   subType?: 'PHONE' | 'SMS' | 'WX'
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      subscribeUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_void> & {
          data: FavSubscribeRequest;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_void, 'xcxUniversity.subscribeUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 取消收藏
       *
       * **path:** /xcx/university/fav/unCollect
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 学位类型: 1：学术学位、2：专业学位
       *   degreeType?: string
       *   // 学习方式: 1：全日制、0：非全日制
       *   learnType?: string
       *   // 专业代码
       *   majorCode?: string
       *   // 学院代码
       *   uniCode?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      unCollectUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_void> & {
          data: MyCollectRequest;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_void, 'xcxUniversity.unCollectUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 取消订阅调剂公告通知
       *
       * **path:** /xcx/university/fav/unSubscribe
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 收藏表主键
       *   id?: number
       *   // 收藏类型
       *   subType?: 'PHONE' | 'SMS' | 'WX'
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      unSubscribeUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_void> & {
          data: FavUnSubscribeRequest;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_void, 'xcxUniversity.unSubscribeUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 我的分数排名
       *
       * **path:** /xcx/university/fightAnalysis/myRank
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 学习方式等于
       *   learnTypeEquals?: string
       *   // 专业代码等于
       *   majorCodeEquals?: string
       *   // 院校属性在。。。之间
       *   propIn?: string[]
       *   // 省份代码在。。。之间
       *   provinceCodeIn?: string[]
       *   // 年份
       *   yearEquals?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   // [title] MyRankVo
       *   data?: {
       *     // 我的排名
       *     myRank?: number
       *     // 总排名
       *     total?: number
       *   }
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      queryMyRankUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_myrankvo> & {
          data: MyRankFilter;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_myrankvo, 'xcxUniversity.queryMyRankUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 一分一段
       *
       * **path:** /xcx/university/fightAnalysis/oneScoreOneRange
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 学习方式等于
       *   learnTypeEquals?: string
       *   // 专业代码等于
       *   majorCodeEquals?: string
       *   // 院校属性在。。。之间
       *   propIn?: string[]
       *   // 省份代码在。。。之间
       *   provinceCodeIn?: string[]
       *   // 年份
       *   yearEquals?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: Array<{
       *     // 初试成绩(区间或只有一个成绩)
       *     first?: number[]
       *     // 排名
       *     rank?: number
       *     // 分数是否命中此区间
       *     scoreHit?: boolean
       *     // 人数
       *     total?: number
       *     uniItems?: Array<{
       *       // 学位类型
       *       degreeType?: string
       *       // 院系所代码
       *       departmentCode?: string
       *       // 院系所名称
       *       departmentName?: string
       *       // 研究方向代码
       *       directionCode?: string
       *       // 研究方向名称
       *       directionName?: string
       *       // 学习方式
       *       learnType?: string
       *       // 专业代码
       *       majorCode?: string
       *       // 专业名称
       *       majorName?: string
       *       // 一级学科代码
       *       oneSubjectCode?: string
       *       // 一级学科名称
       *       oneSubjectName?: string
       *       // 院校属性
       *       props?: string[]
       *       // 门类代码
       *       subjectCategoryCode?: string
       *       // 门类名称
       *       subjectCategoryName?: string
       *       // 院校录取人数
       *       total?: number
       *       // 院校代码
       *       uniCode?: string
       *       // 院校名称
       *       uniName?: string
       *     }>
       *     // 年份
       *     year?: string
       *   }>
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      queryOneScoreOneRangeUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_list_onescoreonerangevo> & {
          data: OneScoreOneRangeFilter;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_list_onescoreonerangevo, 'xcxUniversity.queryOneScoreOneRangeUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 同分去向
       *
       * **path:** /xcx/university/fightAnalysis/sameScore/dest
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 初试成绩浮动范围值
       *   firstScoreFloat?: number
       *   // 学习方式等于
       *   learnTypeEquals?: string
       *   // 专业代码等于
       *   majorCodeEquals?: string
       *   // 当前页
       *   page?: number
       *   // 分页条数
       *   pageSize?: number
       *   // 院校属性在。。。之间
       *   propIn?: string[]
       *   // 省份代码在。。。之间
       *   provinceCodeIn?: string[]
       *   // 年份
       *   yearEquals?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: Array<{
       *     // 录取院校代码
       *     admissionUniCode?: string
       *     // 录取院校名称
       *     admissionUniName?: string
       *     // 初试成绩
       *     first?: number
       *     // 学习方式
       *     learnType?: string
       *     // 专业代码
       *     majorCode?: string
       *     // 专业名称
       *     majorName?: string
       *     // 姓名
       *     name?: string
       *     // 年份
       *     year?: string
       *   }>
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      querySameScoreDestUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_list_samescoredestvo> & {
          data: SameScoreDestFilter;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_list_samescoredestvo, 'xcxUniversity.querySameScoreDestUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 同分去向统计信息
       *
       * **path:** /xcx/university/fightAnalysis/sameScore/dest/statistics
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 初试成绩浮动范围值
       *   firstScoreFloat?: number
       *   // 学习方式等于
       *   learnTypeEquals?: string
       *   // 专业代码等于
       *   majorCodeEquals?: string
       *   // 当前页
       *   page?: number
       *   // 分页条数
       *   pageSize?: number
       *   // 院校属性在。。。之间
       *   propIn?: string[]
       *   // 省份代码在。。。之间
       *   provinceCodeIn?: string[]
       *   // 年份
       *   yearEquals?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   // [title] SameScoreDestStatisticsVo
       *   data?: {
       *     // 最多去向省份
       *     mostProvinceList?: Array<{
       *       // 同分去向的省份代码
       *       provinceCode?: string
       *       // 同分去向的省份名称
       *       provinceName?: string
       *       // 同分去向的院校省份对应人数所占比例
       *       rate?: number
       *       // 同分去向的院校省份对应人数
       *       total?: number
       *     }>
       *     // 最多去向院校
       *     mostUniList?: Array<{
       *       // 同分去向的院校对应人数所占比例
       *       rate?: number
       *       // 同分去向的院校对应人数
       *       total?: number
       *       // 同分去向的院校代码
       *       uniCode?: string
       *       // 同分去向的院校名称
       *       uniName?: string
       *     }>
       *   }
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      sameScoreDestStatisticsUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_samescoredeststatisticsvo> & {
          data: SameScoreDestFilter;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_samescoredeststatisticsvo, 'xcxUniversity.sameScoreDestStatisticsUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 同校去向
       *
       * **path:** /xcx/university/fightAnalysis/sameUni/dest
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 初试成绩浮动范围值
       *   firstScoreFloat?: number
       *   // 学习方式等于
       *   learnTypeEquals?: string
       *   // 专业代码等于
       *   majorCodeEquals?: string
       *   // 当前页
       *   page?: number
       *   // 分页条数
       *   pageSize?: number
       *   // 省份代码在。。。之间
       *   provinceCodeIn?: string[]
       *   // 院校代码等于
       *   uniCodeEquals?: string
       *   // 年份
       *   yearEquals?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: Array<{
       *     // 录取院校代码
       *     admissionUniCode?: string
       *     // 录取院校名称
       *     admissionUniName?: string
       *     // 初试成绩
       *     first?: number
       *     // 学习方式
       *     learnType?: string
       *     // 专业代码
       *     majorCode?: string
       *     // 专业名称
       *     majorName?: string
       *     // 姓名
       *     name?: string
       *     // 年份
       *     year?: string
       *   }>
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      querySameUniDestUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_list_sameunidestvo> & {
          data: SameUniDestFilter;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_list_sameunidestvo, 'xcxUniversity.querySameUniDestUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 同校去向统计信息
       *
       * **path:** /xcx/university/fightAnalysis/sameUni/dest/statistics
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 初试成绩浮动范围值
       *   firstScoreFloat?: number
       *   // 学习方式等于
       *   learnTypeEquals?: string
       *   // 专业代码等于
       *   majorCodeEquals?: string
       *   // 当前页
       *   page?: number
       *   // 分页条数
       *   pageSize?: number
       *   // 省份代码在。。。之间
       *   provinceCodeIn?: string[]
       *   // 院校代码等于
       *   uniCodeEquals?: string
       *   // 年份
       *   yearEquals?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   // [title] SameUniDestStatisticsVo
       *   data?: {
       *     // 最多去向省份
       *     mostProvinceList?: Array<{
       *       // 同校去向的院校省份代码
       *       provinceCode?: string
       *       // 同校去向的院校省份名称
       *       provinceName?: string
       *       // 同校去向的院校省份对应人数所占比例
       *       rate?: number
       *       // 同校去向的院校省份对应人数
       *       total?: number
       *     }>
       *     // 最多去向院校
       *     mostUniList?: Array<{
       *       // 同校去向的院校对应人数所占比例
       *       rate?: number
       *       // 同校去向的院校对应人数
       *       total?: number
       *       // 同校去向的院校代码
       *       uniCode?: string
       *       // 同校去向的院校名称
       *       uniName?: string
       *     }>
       *   }
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      querySameUniDestStatisticsUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_sameunideststatisticsvo> & {
          data: SameUniDestFilter;
        }
      >(
        config: Config
      ): Alova2Method<
        Baseresponse_sameunideststatisticsvo,
        'xcxUniversity.querySameUniDestStatisticsUsingPOST',
        Config
      >;
      /**
       * ---
       *
       * [POST] 拟录取分析
       *
       * **path:** /xcx/university/firstChoice/analysis/year
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 是否含专项计划
       *   hasSpecialPlan?: boolean
       *   // 年份
       *   yearEquals?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   // [title] FirstChoiceYearAnalysisVo
       *   data?: {
       *     // 分数和最高分比较结果描述
       *     compareMaxDesc?: string
       *     // 分数和最低分比较结果描述
       *     compareMinDesc?: string
       *     // 一志愿录取最高分
       *     firstChoiceAdmMax?: number
       *     // 一志愿录取成绩中位数
       *     firstChoiceAdmMedian?: number
       *     // 一志愿录取最低分
       *     firstChoiceAdmMin?: number
       *     // 一志愿拟录取总人数
       *     firstChoiceProposedAdmNum?: number
       *     firstRank?: number
       *   }
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      firstChoiceYearAnalysisUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_firstchoiceyearanalysisvo> & {
          data: FirstChoiceYearAnalysisFilter;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_firstchoiceyearanalysisvo, 'xcxUniversity.firstChoiceYearAnalysisUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 一志愿预估
       *
       * **path:** /xcx/university/firstChoice/predict
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   // [title] FirstChoicePredictVo
       *   data?: {
       *     // 考生类别
       *     candidateType?: string
       *     // 总成绩和国家线比较结果描述
       *     compareLineDesc?: string
       *     // 国家线
       *     nationalLines?: Array<{
       *       // 单科满分等于100的分数线
       *       single100?: number
       *       // 单科满分超过100的分数线
       *       singleUp100?: number
       *       // 总分
       *       total?: number
       *       // 年份
       *       year?: string
       *     }>
       *   }
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      firstChoicePredictUsingPOST<Config extends Alova2MethodConfig<Baseresponse_firstchoicepredictvo>>(
        config?: Config
      ): Alova2Method<Baseresponse_firstchoicepredictvo, 'xcxUniversity.firstChoicePredictUsingPOST', Config>;
      /**
       * ---
       *
       * [GET] 学校级别列表
       *
       * **path:** /xcx/university/levels
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: string[]
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      levelsUsingGET<Config extends Alova2MethodConfig<Baseresponse_list_string>>(
        config?: Config
      ): Alova2Method<Baseresponse_list_string, 'xcxUniversity.levelsUsingGET', Config>;
      /**
       * ---
       *
       * [POST] 院校专业详情
       *
       * **path:** /xcx/university/major/detail
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 学位类型等于
       *   degreeTypeEquals?: string
       *   // 研究方向代码等于
       *   directionCodeEquals?: string
       *   // 学习方式等于
       *   // [required]
       *   learnTypeEquals: string
       *   // 专业代码等于
       *   // [required]
       *   majorCodeEquals: string
       *   // 院校代码等于
       *   // [required]
       *   uniCodeEquals: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: Array<{
       *     // 院校代码
       *     code?: string
       *     // 是否已收藏
       *     collected?: boolean
       *     // 学位类型
       *     degreeType?: string
       *     // 院系所代码
       *     departmentCode?: string
       *     // 院系所名称
       *     departmentName?: string
       *     // 院校简介
       *     description?: string
       *     // 研究方向代码
       *     directionCode?: string
       *     // 研究方向名称
       *     directionName?: string
       *     // 收藏ID
       *     favId?: string
       *     // 学习方式
       *     learnType?: string
       *     // 院校名称
       *     name?: string
       *     // 一级学科代码
       *     oneSubjectCode?: string
       *     // 一级学科名称
       *     oneSubjectName?: string
       *     // 电话订阅
       *     phoneSub?: boolean
       *     // 院校属性
       *     props?: string[]
       *     // 院校地区代码
       *     provinceCode?: string
       *     // 院校地区名称
       *     provinceName?: string
       *     // 院校排名
       *     ranking?: string
       *     // 短信订阅
       *     smsSub?: boolean
       *     // 学科门类代码
       *     subjectCategoryCode?: string
       *     // 学科门类名称
       *     subjectCategoryName?: string
       *     // 二级学科代码
       *     twoSubjectCode?: string
       *     // 二级学科名称
       *     twoSubjectName?: string
       *     // 微信消息订阅
       *     wxSub?: boolean
       *   }>
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      majorDetailUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_list_universitymajordetailvo> & {
          data: MajorDetailFilter;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_list_universitymajordetailvo, 'xcxUniversity.majorDetailUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 注册会员选择专业
       *
       * **path:** /xcx/university/major/queryForRegister
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 专业代码等于或名称类似于
       *   codeEqualsOrNameLikes?: string
       *   // 院系所代码等于
       *   departmentCodeEquals?: string
       *   // 学习方式等于
       *   learnTypeEquals?: string
       *   // 当前页
       *   page?: number
       *   // 分页条数
       *   pageSize?: number
       *   // 院校代码等于
       *   uniCodeEquals?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: Array<{
       *     // 专业代码
       *     code?: string
       *     // 专业名称
       *     name?: string
       *   }>
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      queryMajorForRegisterUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_list_majorqueryforregistervo> & {
          data: MajorQueryForRegisterFilter;
        }
      >(
        config: Config
      ): Alova2Method<
        Baseresponse_list_majorqueryforregistervo,
        'xcxUniversity.queryMajorForRegisterUsingPOST',
        Config
      >;
      /**
       * ---
       *
       * [POST] 前端查询条件数据枚举
       *
       * **path:** /xcx/university/qd/list
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   // [title] QdVo
       *   data?: {
       *     // 初试成绩浮动范围值
       *     firstScoreFloat?: number
       *     // 省份枚举数据
       *     provinceList?: Array<{
       *       // 代码
       *       code?: string
       *       // 名称
       *       name?: string
       *     }>
       *     // 学术学位枚举数据
       *     xsDegreeList?: Array<{
       *       // 子级
       *       children?: Array<XsDegreeQdVo>
       *       // 代码
       *       code?: string
       *       // 名称
       *       name?: string
       *       // 父代码
       *       parentCode?: string
       *     }>
       *     // 专业学位枚举数据
       *     zyDegreeList?: Array<{
       *       // 代码
       *       code?: string
       *       // 名称
       *       name?: string
       *     }>
       *   }
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      listQueryConditionUsingPOST<Config extends Alova2MethodConfig<Baseresponse_qdvo>>(
        config?: Config
      ): Alova2Method<Baseresponse_qdvo, 'xcxUniversity.listQueryConditionUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 按专业查询专业
       *
       * **path:** /xcx/university/queryByMajor
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 学习方式等于
       *   learnTypeEquals?: string
       *   // 专业代码等于
       *   majorCodeEquals?: string
       *   // 专业代码或名称等于
       *   majorCodeEqualsOrNameLikes?: string
       *   minorityCorePlanEquals?: boolean
       *   // 一级学科代码等于
       *   oneSubjectCodeEquals?: string
       *   // 当前页
       *   page?: number
       *   // 分页条数
       *   pageSize?: number
       *   retirePlanEquals?: boolean
       *   // 门类代码等于
       *   subjectCategoryCodeEquals?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: Array<{
       *     // 专业代码
       *     code?: string
       *     // 专业名称
       *     name?: string
       *     // 一级学科代码
       *     oneSubjectCode?: string
       *     // 一级学科名称
       *     oneSubjectName?: string
       *     // 门类代码
       *     subjectCategoryCode?: string
       *     // 门类名称
       *     subjectCategoryName?: string
       *   }>
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      queryByMajorUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_list_universitybymajorvo> & {
          data: UniversityByMajorFilter;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_list_universitybymajorvo, 'xcxUniversity.queryByMajorUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 按招生单位查询院校
       *
       * **path:** /xcx/university/queryByUni
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 学习方式等于
       *   learnTypeEquals?: string
       *   minorityCorePlanEquals?: boolean
       *   // 当前页
       *   page?: number
       *   // 分页条数
       *   pageSize?: number
       *   // 院校属性在。。。之间
       *   propIn?: string[]
       *   // 区域代码等于
       *   provinceCodeEquals?: string
       *   // 省份代码在。。。之间
       *   provinceCodeIn?: string[]
       *   retirePlanEquals?: boolean
       *   // 院校代码等于
       *   uniCodeEquals?: string
       *   // 院校代码或名称等于
       *   uniCodeEqualsOrNameLikes?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: Array<{
       *     // 院校代码
       *     code?: string
       *     // 院校简介
       *     description?: string
       *     // 院校名称
       *     name?: string
       *     // 院校属性
       *     props?: string[]
       *     // 省份
       *     provinceName?: string
       *     // 院校排名来源
       *     rankSource?: string
       *     // 院校排名
       *     ranking?: string
       *     // 院校类型
       *     type?: string
       *   }>
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      queryByUniUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_list_universitybyunivo> & {
          data: UniversityByUniFilter;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_list_universitybyunivo, 'xcxUniversity.queryByUniUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 注册会员选择院校
       *
       * **path:** /xcx/university/queryForRegister
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 院校代码等于或名称类似于
       *   codeEqualsOrNameLikes?: string
       *   // 学习方式等于
       *   learnTypeEquals?: string
       *   // 当前页
       *   page?: number
       *   // 分页条数
       *   pageSize?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: Array<{
       *     // 院校代码
       *     code?: string
       *     // 院校名称
       *     name?: string
       *   }>
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      queryForRegisterUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_list_uniqueryforregistervo> & {
          data: UniQueryForRegisterFilter;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_list_uniqueryforregistervo, 'xcxUniversity.queryForRegisterUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 全院校全专业查询
       *
       * **path:** /xcx/university/queryUniMajor
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 查询关键字
       *   keyword?: string
       *   // 近三年有调剂
       *   last3YearHasAdjust?: boolean
       *   // 学习方式等于
       *   learnTypeEquals?: string
       *   // 专业代码等于
       *   majorCodeEquals?: string
       *   // 专业代码在。。。之间
       *   majorCodeIn?: string[]
       *   // 当前页
       *   page?: number
       *   // 分页条数
       *   pageSize?: number
       *   // 院校属性在。。。之间
       *   propIn?: string[]
       *   // 省份代码等于
       *   provinceCodeEquals?: string
       *   // 省份代码在。。。之间
       *   provinceCodeIn?: string[]
       *   // 今年是否发布调剂公告
       *   publishedAdjust?: boolean
       *   // 院校代码等于
       *   uniCodeEquals?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: Array<{
       *     // 是否已收藏
       *     collected?: boolean
       *     // 学位类型
       *     degreeType?: string
       *     // 院系所代码
       *     departmentCode?: string
       *     // 院系所名称
       *     departmentName?: string
       *     // 研究方向代码
       *     directionCode?: string
       *     // 研究方向名称
       *     directionName?: string
       *     // 学习方式
       *     learnType?: string
       *     // 专业代码
       *     majorCode?: string
       *     // 专业名称
       *     majorName?: string
       *     // 一级学科代码
       *     oneSubjectCode?: string
       *     // 一级学科名称
       *     oneSubjectName?: string
       *     // 院校属性
       *     props?: string[]
       *     // 门类代码
       *     subjectCategoryCode?: string
       *     // 门类名称
       *     subjectCategoryName?: string
       *     // 招生单位代码
       *     uniCode?: string
       *     // 招生单位名称
       *     uniName?: string
       *   }>
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      queryUniMajorUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_list_unimajorvo> & {
          data: Kdhecfebhb;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_list_unimajorvo, 'xcxUniversity.queryUniMajorUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 录取分数维度分析
       *
       * **path:** /xcx/university/scoreChart
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 学习方式等于
       *   // [required]
       *   learnTypeEquals: string
       *   // 专业代码等于
       *   // [required]
       *   majorCodeEquals: string
       *   // 院校代码等于
       *   // [required]
       *   uniCodeEquals: string
       *   // 年份
       *   yearEquals?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   // [title] ScoreChartVo
       *   data?: {
       *     // 调剂直方图数据
       *     adjustItems?: Array<{
       *       // 录取分数区间
       *       scorePeriods?: string[]
       *       // 分数区间对应的录取人数
       *       total?: number
       *     }>
       *     // 调剂来源学校
       *     adjustSourceList?: Array<{
       *       // 一志愿院校代码
       *       oneVolunteerUniversityCode?: string
       *       // 一志愿院校名称
       *       oneVolunteerUniversityName?: string
       *       // 录取人数
       *       total?: number
       *     }>
       *     // 一志愿直方图数据
       *     firstChoiceItems?: Array<ScoreChartItemVo>
       *     // 专业是否是新增专业
       *     majorIsNew?: boolean
       *   }
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      scoreChartUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_scorechartvo> & {
          data: ScoreChartFilter;
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_scorechartvo, 'xcxUniversity.scoreChartUsingPOST', Config>;
      /**
       * ---
       *
       * [GET] 近四年年份枚举
       *
       * **path:** /xcx/university/serverLast3Year
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: string[]
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      serverLast3YearUsingGET<Config extends Alova2MethodConfig<Baseresponse_list_string>>(
        config?: Config
      ): Alova2Method<Baseresponse_list_string, 'xcxUniversity.serverLast3YearUsingGET', Config>;
    };
    xcxFile: {
      /**
       * ---
       *
       * [POST] 上传文件
       *
       * **path:** /xcx/upload
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // file
       *   // [required]
       *   file: Blob
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   data?: string
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      uploadUsingPOST<
        Config extends Alova2MethodConfig<Baseresponse_string> & {
          data: {
            /**
             * file
             * [required]
             */
            file: Blob;
          };
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse_string, 'xcxFile.uploadUsingPOST', Config>;
    };
    xcxWxmp: {
      /**
       * ---
       *
       * [GET] 打开公众号页面
       *
       * **path:** /xcx/weixin/code
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = unknown
       * ```
       */
      openUsingGET<Config extends Alova2MethodConfig<unknown>>(
        config?: Config
      ): Alova2Method<unknown, 'xcxWxmp.openUsingGET', Config>;
      /**
       * ---
       *
       * [GET] 根据临时凭据获取公众号下用户身份, 自动注册/关联会员
       *
       * **path:** /xcx/weixin/code2session
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // code
       *   // [required]
       *   code: string
       *   // appId
       *   // [required]
       *   appId: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code?: number
       *   // [title] 会员
       *   data?: {
       *     // 头像
       *     avatar?: string
       *     // 业务课一代码
       *     biz1Code?: string
       *     // 业务课一名称
       *     biz1Name?: string
       *     // 业务课一成绩
       *     biz1Score?: number
       *     // 业务课二代码
       *     biz2Code?: string
       *     // 业务课二名称
       *     biz2Name?: string
       *     // 业务课二成绩
       *     biz2Score?: number
       *     // 院系所代码
       *     departmentCode?: string
       *     // 院系所名称
       *     departmentName?: string
       *     // 研究方向代码
       *     directionCode?: string
       *     // 研究方向名称
       *     directionName?: string
       *     // 准考证号
       *     examRegistrationNumber?: string
       *     // 一志愿专业代码
       *     firstChoiceDegreeCode?: string
       *     // 一志愿专业名称
       *     firstChoiceDegreeName?: string
       *     // 一志愿学校代码
       *     firstChoiceSchoolCode?: string
       *     // 一志愿学校名称
       *     firstChoiceSchoolName?: string
       *     // 初试成绩
       *     firstScore?: number
       *     // 外语课代码
       *     foreignLanguageCode?: string
       *     // 外语课名称
       *     foreignLanguageName?: string
       *     // 外语成绩
       *     foreignLanguageScore?: number
       *     // 会员ID
       *     id?: string
       *     // 会员手机号
       *     mobile?: string
       *     // 会员名称
       *     name?: string
       *     // 会员昵称
       *     nickName?: string
       *     // 会员微信OPENID
       *     openid?: string
       *     // 政治课代码
       *     politicsCode?: string
       *     // 政治课名称
       *     politicsName?: string
       *     // 政治成绩
       *     politicsScore?: number
       *     // 全日志1/非全日制0
       *     studyType?: string
       *     // 普通用户0/付费用户1
       *     type?: number
       *     // 会员微信unionid
       *     unionid?: string
       *   }
       *   message?: string
       *   more?: boolean
       *   success?: boolean
       *   total?: number
       * }
       * ```
       */
      code2SessionUsingGET<
        Config extends Alova2MethodConfig<Baseresponse1> & {
          params: {
            /**
             * code
             * [required]
             */
            code: string;
            /**
             * appId
             * [required]
             */
            appId: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<Baseresponse1, 'xcxWxmp.code2SessionUsingGET', Config>;
    };
  }

  var Apis: Apis;
}
