<!--
 * @Author: weish<PERSON>
 * @Date: 2025-08-10 21:40:25
 * @LastEditTime: 2025-08-17 17:49:57
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /tianxuan-mini/src/layouts/tabbar.vue
 * 记得注释
-->
<script lang="ts" setup>
const { themeVars } = useTheme()

const router = useRouter()

const route = useRoute()

// 移除暗黑模式相关代码

const { activeTabbar, getTabbarItemValue, setTabbarItemActive, tabbarList } = useTabbar()

function handleTabbarChange({ value }: { value: string }) {
  setTabbarItemActive(value)
  router.pushTab({ name: value })
}

onMounted(() => {
  // #ifdef APP-PLUS
  uni.hideTabBar()
  // #endif
  nextTick(() => {
    if (route.name && route.name !== activeTabbar.value.name) {
      setTabbarItemActive(route.name)
    }
  })
})
</script>

<script lang="ts">
export default {
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <wd-config-provider custom-class="page-wraper" :theme-vars="themeVars">
    <slot />
    <wd-tabbar
      :model-value="activeTabbar.name" bordered safe-area-inset-bottom placeholder fixed
      @change="handleTabbarChange"
    >
      <wd-tabbar-item
        v-for="(item, index) in tabbarList" :key="index" :name="item.name"
        :value="getTabbarItemValue(item.name)" :title="item.title" :icon="item.icon"
      />
    </wd-tabbar>
    <!-- #ifdef MP-WEIXIN -->
    <privacy-popup />
    <!-- #endif -->
    <wd-notify />
    <wd-message-box />
    <wd-toast />
    <global-loading />
    <global-toast />
    <global-message />
  </wd-config-provider>
</template>

<style lang="scss">
.page-wraper {
  min-height: calc(100vh - var(--window-top));
  box-sizing: border-box;
  background: #f9f9f9;
}
</style>
