<!--
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-01-17 10:00:00
 * @LastEditTime: 2025-08-17 22:44:01
 * @LastEditors: weisheng
 * @Description: 定制专属调剂报告块组件
 * @FilePath: /tianxuan-mini/src/pages/index/components/HomeCustomReport.vue
 * 记得注释
-->
<script setup lang="ts">
// 组件逻辑
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="relative mt-3 box-border h-152rpx w-full px-3 py-2">
    <view class="relative z-1 flex items-center px-3">
      <text class="text-##223261 mr-2 text-5 font-700 line-height-normal">
        定制专属调剂报告
      </text>
      <image
        src="@/static/icon/ic_right_blue_20.svg"
        mode="widthFix"
        class="h-5 w-5"
      />
    </view>
    <view class="relative z-1 mt-1 px-3 text-28rpx text-#797C84 line-height-normal">
      获取稳、保、冲院校上岸指南
    </view>
    <image
      src="@/static/images/img_dingzhizhuanshutiojibaogao.png"
      mode="widthFix"
      class="absolute left-3 top-0 z-0 w-702rpx"
    />
  </view>
</template>

<style lang="scss" scoped>
// 组件样式
</style>
