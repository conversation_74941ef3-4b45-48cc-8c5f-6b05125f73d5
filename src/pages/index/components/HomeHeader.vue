<!--
 * @Author: weish<PERSON>
 * @Date: 2025-06-12 23:26:56
 * @LastEditTime: 2025-08-17 18:43:31
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /tianxuan-mini/src/pages/index/components/HomeHeader.vue
 * 记得注释
-->
<script setup lang="ts">
import HomeNavbar from './HomeNavbar.vue'
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="home-header fixed left-0 top-0 w-100vw bg-[var(--wot-color-theme)]">
    <home-navbar use-title />
    <view class="relative h-256rpx w-full">
      <image
        src="@/static/images/tiaojibao_header.svg" mode="widthFix"
        class="relative top-[-32rpx] mx-auto block w-606rpx"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.home-header {
    clip-path: ellipse(100% 100% at 50% 0%);

  :deep() {
    .wd-navbar__content {
      padding-right: var(--navbar-right-padding);
      display: flex;
      box-sizing: border-box;
    }

    .wd-navbar__left {
      width: 0 !important;
    }

    .wd-navbar__title {
      max-width: unset !important;
      margin: 0 !important;
      flex: 1 1 auto;
      box-sizing: border-box;
      padding: 0 24rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;

      view {
        display: flex;
        width: 100%;
      }
    }
  }

}
</style>
