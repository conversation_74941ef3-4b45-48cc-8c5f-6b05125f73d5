<!--
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-01-17 10:00:00
 * @LastEditTime: 2025-08-17 22:55:46
 * @LastEditors: weisheng
 * @Description: 定制专属调剂报告块组件
 * @FilePath: /tianxuan-mini/src/pages/index/components/HomeAdjustmentAssistant.vue
 * 记得注释
-->
<script setup lang="ts">
import tongfenquxiang from '@/static/icon/ic_tongfenquxiang_40.svg'
import tongxiaoquxiang from '@/static/icon/ic_tongxiaoquxiang_40.svg'
import yitiaoji from '@/static/icon/ic_yitiaoji_40.svg'
import xiangsi from '@/static/icon/ic_xiangsi_40.svg'
import duibi from '@/static/icon/ic_duibi_40.svg'

const funcList = shallowRef([
  {
    title: '同分去向',
    icon: tongfenquxiang,
  },
  {
    title: '同校区乡',
    icon: tongxiaoquxiang,
  },
  {
    title: '易调剂',
    icon: yitiaoji,
  },
  {
    title: '与一志愿相似',
    icon: xiangsi,
  },
  {
    title: '院校对比',
    icon: duibi,
  },
])
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="relative mt-4 box-border w-full px-3">
    <view class="mb-2 text-36rpx text-#202124">
      调剂工具(助手)
    </view>
    <view class="flex">
      <view v-for="item in funcList" :key="item.title" class="flex flex-auto flex-col items-center justify-center">
        <image :src="item.icon" class="mb-1 h-10 w-10" mode="widthFix" />
        <view class="text-3 text-#202124">
          {{ item.title }}
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
// 组件样式
</style>
