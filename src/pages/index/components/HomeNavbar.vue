<!--
 * @Author: weish<PERSON>
 * @Date: 2025-08-17 18:38:59
 * @LastEditTime: 2025-08-17 18:47:34
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /tianxuan-mini/src/pages/index/components/HomeNavbar.vue
 * 记得注释
-->
<script setup lang="ts">
import type { CSSProperties } from 'vue'

const props = defineProps({
  useTitle: {
    type: Boolean,
    default: false,
  },
  transparent: {
    type: Boolean,
    default: true,
  },
  /** 自定义类名 */
  customClass: {
    type: String,
    default: '',
  },
  /** 自定义样式 */
  customStyle: {
    type: String,
    default: '',
  },
})

const { capsule } = storeToRefs(useDeviceInfo())

const navbarStyle = computed(() => {
  const style: CSSProperties = {
    paddingRight: `${capsule.value.right + capsule.value.width}px`,
  }
  if (props.transparent) {
    style.backgroundColor = 'transparent'
  }
  return `--navbar-right-padding: ${capsule.value.right + capsule.value.width}px;${CommonUtil.objToStyle(style)}${props.customStyle};`
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="nav-bar w-100vw bg-[var(--wot-color-theme)]">
    <wd-navbar
      :custom-style="navbarStyle" :bordered="false" safe-area-inset-top :left-arrow="false"
      placeholder
    >
      <template #title>
        <image v-if="props.useTitle" src="@/static/images/tiaojibao.svg" mode="widthFix" class="w-148rpx" />
      </template>
    </wd-navbar>
  </view>
</template>

<style lang="scss" scoped>
.nav-bar {
  :deep() {
    .wd-navbar__content {
      padding-right: var(--navbar-right-padding);
      display: flex;
      box-sizing: border-box;
    }

    .wd-navbar__left {
      width: 0 !important;
    }

    .wd-navbar__title {
      max-width: unset !important;
      margin: 0 !important;
      flex: 1 1 auto;
      box-sizing: border-box;
      padding: 0 24rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;

      view {
        display: flex;
        width: 100%;
      }
    }
  }
}
</style>
