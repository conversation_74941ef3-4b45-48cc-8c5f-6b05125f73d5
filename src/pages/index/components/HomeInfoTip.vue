<!--
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-01-17 10:00:00
 * @LastEditTime: 2025-01-17 10:00:00
 * @LastEditors: weisheng
 * @Description: 完善一志愿和初试成绩提示块组件
 * @FilePath: /tianxuan-mini/src/pages/index/components/HomeInfoTip.vue
 * 记得注释
-->
<script setup lang="ts">
// 组件逻辑
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="relative mx-auto box-border h-72rpx w-702rpx flex items-center justify-between px-3 text-28rpx text-white font-500">
    <text class="z-1">
      完善一志愿和初试成绩，为您推荐调剂专业
    </text>
    <image src="@/static/images/home_info.svg" mode="widthFix" class="absolute left-0 top-0 h-72rpx w-702rpx" />
    <view class="z-1 flex items-center">
      <text>去完善</text>
      <text class="i-tdesign-chevron-right-s" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
// 组件样式
</style>
