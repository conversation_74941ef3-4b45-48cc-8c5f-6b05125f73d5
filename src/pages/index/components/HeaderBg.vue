<!--
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-08-17 17:51:25
 * @LastEditTime: 2025-08-17 18:11:03
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /tianxuan-mini/src/pages/index/components/HeaderBg.vue
 * 记得注释
-->
<script setup lang="ts">

</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="header-bg">
    <view class="bg-content" />
    <view class="curve-overlay" />
  </view>
</template>

<style lang="scss" scoped>
.header-bg {
  height: 256rpx;
  width: 100%;
  background: var(--wot-color-theme);

  /* 根据你提供的SVG路径创建的弧形效果 */
  // clip-path: path('M0,0 L100%,0 L100%,69% C100%,69% 69.5%,84.4% 50%,84.4% C30.5%,84.4% 0,69% 0,69% Z');

  /* 备选方案1: 简化版本，更容易调整 */
  clip-path: path('M0,0 L100%,0 L100%,70% Q50%,85% 0,70% Z');

  /* 备选方案2: 使用贝塞尔曲线创建更平滑的弧形 */
  // clip-path: path('M0,0 L100%,0 L100%,65% C75%,80% 25%,80% 0,65% Z');

  /* 备选方案3: 椭圆形状（之前的方法） */
  // clip-path: ellipse(100% 100% at 50% 0%);

  /* 备选方案4: 简单圆角 */
  // border-radius: 0 0 50rpx 50rpx;
}
</style>
