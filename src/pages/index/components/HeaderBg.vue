<!--
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-08-17 17:51:25
 * @LastEditTime: 2025-08-17 18:07:46
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /tianxuan-mini/src/pages/index/components/HeaderBg.vue
 * 记得注释
-->
<script setup lang="ts">

</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="header-bg" />
</template>

<style lang="scss" scoped>
.header-bg {
  height: 256rpx;
  width: 100%;
  background: var(--wot-color-theme);

  /* 方法1: 使用 border-radius 创建底部弧度 */
  // border-radius: 0 0 50rpx 50rpx;

  /* 或者使用更大的弧度值创建更明显的弧形 */
  // border-radius: 0 0 100% 100%;

  /* 方法2: 使用伪元素 + border-radius 创建更自然的弧形 */
  // position: relative;
  // &::after {
  //   content: '';
  //   position: absolute;
  //   bottom: -50rpx;
  //   left: 0;
  //   width: 100%;
  //   height: 100rpx;
  //   background: var(--wot-color-theme);
  //   border-radius: 50%;
  //   transform: scaleY(0.5);
  // }

  /* 方法3: 使用 clip-path 创建精确的弧形 */
  clip-path: ellipse(100% 100% at 50% 0%);

  /* 方法4: 使用 SVG 路径 */
  // clip-path: path('M0,0 L100%,0 L100%,80% Q50%,100% 0,80% Z');
}
</style>
