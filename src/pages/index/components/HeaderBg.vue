<!--
 * @Author: weish<PERSON>
 * @Date: 2025-08-17 17:51:25
 * @LastEditTime: 2025-08-17 17:59:49
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /tianxuan-mini/src/pages/index/cmp/HeaderBg.vue
 * 记得注释
-->
<script setup lang="ts">

</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="header-bg h-256rpx w-full rounded-t-0 rounded-bl-[80%_10%] rounded-br-[80%_10%] bg-[var(--wot-color-theme)]">
    2323
  </view>
</template>

<style lang="scss" scoped>
.header-bg {
  color: red;
}
</style>
