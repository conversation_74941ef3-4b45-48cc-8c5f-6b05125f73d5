<!--
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-01-17 10:00:00
 * @LastEditTime: 2025-08-17 23:12:56
 * @LastEditors: weisheng
 * @Description: 定制专属调剂报告块组件
 * @FilePath: /tianxuan-mini/src/pages/index/components/HomeTarget.vue
 * 记得注释
-->
<script setup lang="ts">
// 硕士点数据类型定义
interface MasterPoint {
  id: string
  universityName: string
  majorName: string
  degreeType: string
  admissionScore: number
  admissionRank: number
  successRate: number
  isRecommended?: boolean
}

// 模拟硕士点数据
const masterPoints = ref<MasterPoint[]>([
  {
    id: '1',
    universityName: '北京大学',
    majorName: '计算机科学与技术',
    degreeType: '学术学位',
    admissionScore: 385,
    admissionRank: 15,
    successRate: 85,
    isRecommended: true,
  },
  {
    id: '2',
    universityName: '清华大学',
    majorName: '软件工程',
    degreeType: '专业学位',
    admissionScore: 390,
    admissionRank: 12,
    successRate: 78,
    isRecommended: true,
  },
  {
    id: '3',
    universityName: '复旦大学',
    majorName: '人工智能',
    degreeType: '学术学位',
    admissionScore: 375,
    admissionRank: 25,
    successRate: 72,
  },
])

const icon = `<svg width="20" height="24" viewBox="0 0 20 24" fill="none" xmlns="">
<path d="M0 4C0 1.79086 1.79086 0 4 0H16C18.2091 0 20 1.79086 20 4V15.7352C20 17.1403 19.2628 18.4423 18.058 19.1652L10.5145 23.6913C10.1978 23.8813 9.80219 23.8813 9.4855 23.6913L1.94202 19.1652C0.737196 18.4423 0 17.1403 0 15.7352V4Z" fill="url(#paint0_linear_1492_3789)"/>
<g filter="url(#filter0_d_1492_3789)">
<path d="M11.9004 6.26367V15H9.48633V9.27539C9.0957 9.57227 8.7168 9.8125 8.34961 9.99609C7.98633 10.1797 7.5293 10.3555 6.97852 10.5234V8.56641C7.79102 8.30469 8.42188 7.99023 8.87109 7.62305C9.32031 7.25586 9.67188 6.80273 9.92578 6.26367H11.9004Z" fill="white"/>
</g>
<defs>
<filter id="filter0_d_1492_3789" x="3.97852" y="4.26367" width="10.9219" height="14.7363" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.428305 0 0 0 0 0.142982 0 0 0 0 0.134783 0 0 0 0.6 0"/>
<feBlend mode="overlay" in2="BackgroundImageFix" result="effect1_dropShadow_1492_3789"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1492_3789" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1492_3789" x1="-0.700855" y1="-2.30769" x2="28.7313" y2="6.63061" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF8559"/>
<stop offset="1" stop-color="#FF3A45"/>
</linearGradient>
</defs>
</svg>
`

// 点击卡片事件
function handleCardClick(item: MasterPoint) {
  console.log('点击硕士点卡片:', item)
  // 这里可以跳转到详情页面
}
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="relative mt-4 box-border w-full px-3">
    <view class="mb-2 flex items-center justify-between text-36rpx text-#202124">
      <view>目标硕士点</view>
      <view class="flex items-center text-28rpx text-#797C84">
        <text>查看全部</text>
        <text class="i-tdesign-chevron-right-s" />
      </view>
    </view>

    <view class="list">
      <view
        v-for="item in masterPoints"
        :key="item.id"
        class="list-card"
        @click="handleCardClick(item)"
      >
        <!-- 推荐标签 -->
        <view v-if="item.isRecommended" class="recommend-tag">
          <text class="i-tdesign-star-filled text-3 text-white" />
          <text class="ml-1 text-3 text-white">
            推荐
          </text>
        </view>

        <!-- 卡片内容 -->
        <view class="card-content">
          <!-- 学校和专业信息 -->
          <view class="university-info">
            <text class="university-name">
              {{ item.universityName }}
            </text>
            <text class="degree-type">
              {{ item.degreeType }}
            </text>
          </view>

          <view class="major-name">
            {{ item.majorName }}
          </view>

          <!-- 分数和排名信息 -->
          <view class="score-info">
            <view class="score-item">
              <text class="label">
                录取分数
              </text>
              <text class="value">
                {{ item.admissionScore }}分
              </text>
            </view>
            <view class="score-item">
              <text class="label">
                录取排名
              </text>
              <text class="value">
                前{{ item.admissionRank }}名
              </text>
            </view>
          </view>

          <!-- 成功率 -->
          <view class="success-rate">
            <text class="rate-label">
              调剂成功率
            </text>
            <view class="rate-bar">
              <view
                class="rate-fill"
                :style="{ width: `${item.successRate}%` }"
              />
            </view>
            <text class="rate-value">
              {{ item.successRate }}%
            </text>
          </view>
        </view>

        <!-- 右箭头 -->
        <view class="arrow">
          <text class="i-tdesign-chevron-right text-5 text-#C8C9CC" />
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.list-card {
  position: relative;
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.12);
  }
}

.recommend-tag {
  position: absolute;
  top: 0;
  right: 32rpx;
  background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
  border-radius: 0 0 16rpx 16rpx;
  padding: 8rpx 16rpx;
  display: flex;
  align-items: center;
  z-index: 1;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.university-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.university-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #202124;
  line-height: 1.2;
}

.degree-type {
  background: #F0F9FF;
  color: #0369A1;
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  border: 1rpx solid #BAE6FD;
}

.major-name {
  font-size: 28rpx;
  color: #4B5563;
  font-weight: 500;
  line-height: 1.3;
}

.score-info {
  display: flex;
  gap: 32rpx;
}

.score-item {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.label {
  font-size: 24rpx;
  color: #9CA3AF;
}

.value {
  font-size: 28rpx;
  color: #1F2937;
  font-weight: 600;
}

.success-rate {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.rate-label {
  font-size: 24rpx;
  color: #6B7280;
  min-width: 120rpx;
}

.rate-bar {
  flex: 1;
  height: 12rpx;
  background: #F3F4F6;
  border-radius: 6rpx;
  overflow: hidden;
}

.rate-fill {
  height: 100%;
  background: linear-gradient(90deg, #10B981, #34D399);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.rate-value {
  font-size: 24rpx;
  color: #10B981;
  font-weight: 600;
  min-width: 60rpx;
  text-align: right;
}

.arrow {
  margin-left: 16rpx;
  display: flex;
  align-items: center;
}
</style>
