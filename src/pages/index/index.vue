<!--
 * @Author: weish<PERSON>
 * @Date: 2025-08-17 15:56:07
 * @LastEditTime: 2025-08-17 23:08:39
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /tianxuan-mini/src/pages/index/index.vue
 * 记得注释
-->
<script setup lang="ts">
import HomeHeader from './components/HomeHeader.vue'
import HomeNavbar from './components/HomeNavbar.vue'
import HomeInfoTip from './components/HomeInfoTip.vue'
import HomeMainFeatures from './components/HomeMainFeatures.vue'
import HomeCustomReport from './components/HomeCustomReport.vue'
import HomeAdjustmentAssistant from './components/HomeAdjustmentAssistant.vue'
import HomeTarget from './components/HomeTarget.vue'
</script>

<template>
  <view class="box-border w-100vw">
    <HomeHeader />
    <HomeNavbar />
    <view class="relative z-1 pt-5">
      <HomeInfoTip />
      <HomeMainFeatures />
      <HomeCustomReport />
      <HomeAdjustmentAssistant />
      <HomeTarget />
    </view>
  </view>
</template>

<route lang="json">
{
  "name": "home",
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "首页",
    "navigationStyle": "custom"
  }
}
</route>
