<script setup lang="ts">
import type { CSSProperties } from 'vue'

const props = defineProps({
  /**
   * 是否固定
   */
  fixed: {
    type: Boolean,
    default: true,
  },
  /** 自定义类名 */
  customClass: {
    type: String,
    default: '',
  },
  /** 自定义样式 */
  customStyle: {
    type: String,
    default: '',
  },

})

const { capsule } = storeToRefs(useDeviceInfo())

const navbarStyle = computed(() => {
  const style: CSSProperties = {
    paddingRight: `${capsule.value.right + capsule.value.width}px`,
    background: 'var(--wot-color-theme)',
  }
  return `--navbar-right-padding: ${capsule.value.right + capsule.value.width}px;${CommonUtil.objToStyle(style)}${props.customStyle}`
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="cart-nav-bar">
    <wd-navbar :custom-style="navbarStyle" :bordered="false" :fixed="fixed" safe-area-inset-top :left-arrow="false" placeholder>
      <template #title>
        <image
          src="@/static/images/tiaojibao.svg"
          mode="widthFix"
          class="w-148rpx"
        />
      </template>
    </wd-navbar>
  </view>
</template>

<style lang="scss" scoped>
.cart-nav-bar {

  :deep() {
    .wd-navbar__content{
      padding-right: var(--navbar-right-padding);
      display: flex;
      box-sizing: border-box;
    }
    .wd-navbar__left{
      width: 0 !important;
    }

    .wd-navbar__title{
      max-width: unset !important;
      margin: 0 !important;
      flex: 1 1 auto;
      box-sizing: border-box;
      padding: 0 24rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;

      view {
        display: flex;
        width: 100%;
      }
    }
  }

}
</style>
